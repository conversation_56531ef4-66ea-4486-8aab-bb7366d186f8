import { defineConfig } from 'vite';

export default defineConfig({
  assetsInclude: ['**/*.vert', '**/*.frag'],
  server: {
    https: false, // Set to true for HTTPS in production
    host: true,
    port: 5173
  },
  build: {
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          three: ['three'],
          mediapipe: ['@mediapipe/tasks-vision']
        }
      }
    }
  },
  optimizeDeps: {
    include: ['three', '@mediapipe/tasks-vision']
  }
});
