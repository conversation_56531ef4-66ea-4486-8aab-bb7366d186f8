import { HandLandmarker, FilesetResolver, type NormalizedLandmark } from '@mediapipe/tasks-vision';

// Constants for gesture mapping
export const AMP_MIN = 0.05;
export const AMP_MAX = 0.8;
export const FREQ_MIN = 0.8;
export const FREQ_MAX = 6.0;

// Hand landmark indices
const WRIST = 0;
const THUMB_TIP = 4;
const INDEX_TIP = 8;
const INDEX_MCP = 9;
const PINKY_MCP = 17;

// Smoothing helper class
class EMAFilter {
  private values: Map<string, number> = new Map();
  
  update(key: string, value: number, alpha: number = 0.25): number {
    const prev = this.values.get(key) || value;
    const smoothed = prev + alpha * (value - prev);
    this.values.set(key, smoothed);
    return smoothed;
  }
  
  get(key: string): number {
    return this.values.get(key) || 0;
  }
}

// Hand gesture parameters
export interface HandParams {
  amplitude: number;
  frequency: number;
  paletteIndex: number;
  handDetected: boolean;
}

// Event system for parameter updates
type HandEventCallback = (params: HandParams) => void;

export class HandTracker {
  private handLandmarker: HandLandmarker | null = null;
  private video: HTMLVideoElement | null = null;
  private isInitialized = false;
  private isTracking = false;
  
  // Smoothing filters
  private emaFilter = new EMAFilter();
  
  // Current parameters
  private params: HandParams = {
    amplitude: AMP_MIN,
    frequency: FREQ_MIN,
    paletteIndex: 0,
    handDetected: false
  };
  
  // Palm flip detection
  private lastPalmNormalZ = 0;
  private lastFlipTime = 0;
  
  // Event callbacks
  private callbacks: HandEventCallback[] = [];
  
  constructor() {
    this.initializeMediaPipe();
  }
  
  private async initializeMediaPipe(): Promise<void> {
    try {
      const vision = await FilesetResolver.forVisionTasks(
        'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
      );
      
      this.handLandmarker = await HandLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task',
          delegate: 'GPU'
        },
        runningMode: 'VIDEO',
        numHands: 1,
        minHandDetectionConfidence: 0.5,
        minHandPresenceConfidence: 0.5,
        minTrackingConfidence: 0.5
      });
      
      this.isInitialized = true;
      console.log('MediaPipe HandLandmarker initialized');
    } catch (error) {
      console.error('Failed to initialize MediaPipe:', error);
      throw error;
    }
  }
  
  async startCamera(): Promise<HTMLVideoElement> {
    if (!this.isInitialized) {
      throw new Error('HandTracker not initialized');
    }
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'user',
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });
      
      this.video = document.getElementById('video-background') as HTMLVideoElement;
      if (!this.video) {
        throw new Error('Video element not found');
      }
      
      this.video.srcObject = stream;
      this.video.addEventListener('loadeddata', () => {
        this.startTracking();
      });
      
      return this.video;
    } catch (error) {
      console.error('Failed to start camera:', error);
      throw error;
    }
  }
  
  private startTracking(): void {
    if (!this.video || !this.handLandmarker || this.isTracking) return;
    
    this.isTracking = true;
    this.trackFrame();
  }
  
  private trackFrame(): void {
    if (!this.isTracking || !this.video || !this.handLandmarker) return;
    
    const startTimeMs = performance.now();
    
    try {
      const results = this.handLandmarker.detectForVideo(this.video, startTimeMs);
      this.processResults(results.landmarks);
    } catch (error) {
      console.error('Hand tracking error:', error);
    }
    
    // Continue tracking
    requestAnimationFrame(() => this.trackFrame());
  }
  
  private processResults(landmarks: NormalizedLandmark[][]): void {
    if (landmarks.length === 0) {
      // No hand detected - gradually return to defaults
      this.params.handDetected = false;
      this.relaxToDefaults();
    } else {
      // Hand detected - process gestures
      this.params.handDetected = true;
      const handLandmarks = landmarks[0];
      this.processGestures(handLandmarks);
    }
    
    // Notify callbacks
    this.notifyCallbacks();
  }
  
  private processGestures(landmarks: NormalizedLandmark[]): void {
    // 1. Pinch gesture for amplitude
    this.processPinchGesture(landmarks);
    
    // 2. Wrist height for frequency
    this.processWristHeight(landmarks);
    
    // 3. Palm flip for palette cycling
    this.processPalmFlip(landmarks);
  }
  
  private processPinchGesture(landmarks: NormalizedLandmark[]): void {
    const thumbTip = landmarks[THUMB_TIP];
    const indexTip = landmarks[INDEX_TIP];
    const wrist = landmarks[WRIST];
    const indexMcp = landmarks[INDEX_MCP];
    
    // Calculate pinch distance normalized by hand scale
    const pinchDist = this.distance3D(thumbTip, indexTip);
    const handScale = this.distance3D(wrist, indexMcp);
    const normalizedPinch = pinchDist / handScale;
    
    // Map to amplitude with hysteresis
    const rawAmplitude = Math.max(0, Math.min(1, 1.2 - normalizedPinch));
    const amplitude = AMP_MIN + rawAmplitude * (AMP_MAX - AMP_MIN);
    
    // Apply smoothing
    this.params.amplitude = this.emaFilter.update('amplitude', amplitude, 0.3);
  }
  
  private processWristHeight(landmarks: NormalizedLandmark[]): void {
    const wrist = landmarks[WRIST];
    
    // Convert to NDC with inversion (top=1, bottom=0)
    const wristHeight = 1.0 - wrist.y;
    
    // Map to frequency
    const frequency = FREQ_MIN + wristHeight * (FREQ_MAX - FREQ_MIN);
    
    // Apply smoothing
    this.params.frequency = this.emaFilter.update('frequency', frequency, 0.25);
  }
  
  private processPalmFlip(landmarks: NormalizedLandmark[]): void {
    const wrist = landmarks[WRIST];
    const indexMcp = landmarks[INDEX_MCP];
    const pinkyMcp = landmarks[PINKY_MCP];
    
    // Calculate palm normal using cross product
    const v1 = {
      x: indexMcp.x - wrist.x,
      y: indexMcp.y - wrist.y,
      z: indexMcp.z - wrist.z
    };
    
    const v2 = {
      x: pinkyMcp.x - wrist.x,
      y: pinkyMcp.y - wrist.y,
      z: pinkyMcp.z - wrist.z
    };
    
    // Cross product
    const normal = {
      x: v1.y * v2.z - v1.z * v2.y,
      y: v1.z * v2.x - v1.x * v2.z,
      z: v1.x * v2.y - v1.y * v2.x
    };
    
    // Normalize
    const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y + normal.z * normal.z);
    if (length > 0) {
      normal.z /= length;
    }
    
    // Detect flip with debouncing
    const currentTime = performance.now();
    const zDiff = normal.z - this.lastPalmNormalZ;
    
    if (Math.abs(normal.z) > 0.3 && Math.abs(zDiff) > 0.6) {
      if (currentTime - this.lastFlipTime > 250) { // 250ms debounce
        this.params.paletteIndex = (this.params.paletteIndex + 1) % 6; // Assuming 6 palettes
        this.lastFlipTime = currentTime;
      }
    }
    
    this.lastPalmNormalZ = normal.z;
  }
  
  private relaxToDefaults(): void {
    // Gradually return to default values when hand is lost
    this.params.amplitude = this.emaFilter.update('amplitude', AMP_MIN, 0.02);
    this.params.frequency = this.emaFilter.update('frequency', (FREQ_MIN + FREQ_MAX) / 2, 0.02);
  }
  
  private distance3D(p1: NormalizedLandmark, p2: NormalizedLandmark): number {
    const dx = p1.x - p2.x;
    const dy = p1.y - p2.y;
    const dz = p1.z - p2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
  
  // Event system
  onParamsUpdate(callback: HandEventCallback): void {
    this.callbacks.push(callback);
  }
  
  private notifyCallbacks(): void {
    this.callbacks.forEach(callback => callback({ ...this.params }));
  }
  
  // Public methods
  getParams(): HandParams {
    return { ...this.params };
  }
  
  cyclePalette(): void {
    this.params.paletteIndex = (this.params.paletteIndex + 1) % 6;
    this.notifyCallbacks();
  }
  
  stop(): void {
    this.isTracking = false;
    if (this.video && this.video.srcObject) {
      const stream = this.video.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
    }
  }
}
