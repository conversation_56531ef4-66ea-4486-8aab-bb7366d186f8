import { Color } from 'three';

export interface ColorPalette {
  name: string;
  colors: Color[];
}

// Predefined color palettes
export const PALETTES: ColorPalette[] = [
  {
    name: '8BIT',
    colors: [
      new Color(0x000000), // Black
      new Color(0x1D2B53), // Dark blue
      new Color(0x7E2553), // Dark purple
      new Color(0x008751), // Dark green
      new Color(0xAB5236), // Brown
      new Color(0x5F574F), // Dark grey
      new Color(0xC2C3C7), // Light grey
      new Color(0xFFF1E8), // White
      new Color(0xFF004D), // Red
      new Color(0xFFA300), // Orange
      new Color(0xFFEC27), // Yellow
      new Color(0x00E436), // Green
      new Color(0x29ADFF), // Blue
      new Color(0x83769C), // Indigo
      new Color(0xFF77A8), // Pink
      new Color(0xFFCCAA)  // Peach
    ]
  },
  
  {
    name: 'VIRIDIS',
    colors: [
      new Color(0x440154), // Deep purple
      new Color(0x482777), // Purple
      new Color(0x3F4A8A), // Blue-purple
      new Color(0x31678E), // Blue
      new Color(0x26838F), // Teal
      new Color(0x1F9D8A), // Green-teal
      new Color(0x6CCE5A), // Green
      new Color(0xB6DE2B), // Yellow-green
      new Color(0xFEE825), // Yellow
      new Color(0xFDE725), // Bright yellow
      new Color(0xA0DA39), // Light green
      new Color(0x4AC16D), // Medium green
      new Color(0x1FA187), // Dark teal
      new Color(0x277F8E), // Dark blue
      new Color(0x365C8D), // Navy
      new Color(0x46327E)  // Dark purple
    ]
  },
  
  {
    name: 'MAGMA',
    colors: [
      new Color(0x000004), // Almost black
      new Color(0x0B0924), // Dark purple
      new Color(0x231151), // Purple
      new Color(0x410F75), // Magenta
      new Color(0x5F187F), // Purple-red
      new Color(0x7B2382), // Red-purple
      new Color(0x982D80), // Red
      new Color(0xB73779), // Pink-red
      new Color(0xD3436E), // Pink
      new Color(0xEB5760), // Orange-red
      new Color(0xF8765C), // Orange
      new Color(0xFD9969), // Light orange
      new Color(0xFEBF84), // Peach
      new Color(0xFDE4A6), // Light peach
      new Color(0xFCFDBF), // Very light yellow
      new Color(0xFCFFA4)  // Pale yellow
    ]
  },
  
  {
    name: 'NEON',
    colors: [
      new Color(0xFF0080), // Hot pink
      new Color(0xFF0040), // Pink-red
      new Color(0xFF4000), // Red-orange
      new Color(0xFF8000), // Orange
      new Color(0xFFFF00), // Yellow
      new Color(0x80FF00), // Yellow-green
      new Color(0x00FF00), // Green
      new Color(0x00FF80), // Green-cyan
      new Color(0x00FFFF), // Cyan
      new Color(0x0080FF), // Cyan-blue
      new Color(0x0000FF), // Blue
      new Color(0x4000FF), // Blue-purple
      new Color(0x8000FF), // Purple
      new Color(0xFF00FF), // Magenta
      new Color(0xFF0040), // Pink
      new Color(0xFF8080)  // Light pink
    ]
  },
  
  {
    name: 'RETRO_SUN',
    colors: [
      new Color(0x2D1B69), // Deep purple
      new Color(0x11213A), // Dark blue
      new Color(0x19647E), // Teal
      new Color(0x28A99C), // Cyan
      new Color(0x3BCEAC), // Light cyan
      new Color(0x0EF5D3), // Bright cyan
      new Color(0x5CDB95), // Green
      new Color(0x8EE4AF), // Light green
      new Color(0xF38BA8), // Pink
      new Color(0xF5A623), // Orange
      new Color(0xF7931E), // Dark orange
      new Color(0xFF6B6B), // Red
      new Color(0xFF8E53), // Orange-red
      new Color(0xFFAD5A), // Light orange
      new Color(0xFFD93D), // Yellow
      new Color(0xFFE66D)  // Light yellow
    ]
  },
  
  {
    name: 'ICE_FIRE',
    colors: [
      new Color(0x000428), // Deep blue
      new Color(0x004e92), // Blue
      new Color(0x009ffd), // Light blue
      new Color(0x00d2ff), // Cyan
      new Color(0x66ffff), // Light cyan
      new Color(0xccffff), // Very light cyan
      new Color(0xffffff), // White
      new Color(0xffcccc), // Light pink
      new Color(0xff6666), // Pink
      new Color(0xff0000), // Red
      new Color(0xcc0000), // Dark red
      new Color(0x990000), // Very dark red
      new Color(0xff3300), // Red-orange
      new Color(0xff6600), // Orange
      new Color(0xff9900), // Yellow-orange
      new Color(0xffcc00)  // Yellow
    ]
  }
];

// Utility functions
export function getPalette(index: number): ColorPalette {
  return PALETTES[index % PALETTES.length];
}

export function getPaletteNames(): string[] {
  return PALETTES.map(p => p.name);
}

export function createPaletteTexture(palette: ColorPalette): Float32Array {
  const data = new Float32Array(palette.colors.length * 3);
  
  palette.colors.forEach((color, i) => {
    data[i * 3] = color.r;
    data[i * 3 + 1] = color.g;
    data[i * 3 + 2] = color.b;
  });
  
  return data;
}

// Color interpolation utilities
export function interpolateColor(color1: Color, color2: Color, t: number): Color {
  const result = new Color();
  result.r = color1.r + (color2.r - color1.r) * t;
  result.g = color1.g + (color2.g - color1.g) * t;
  result.b = color1.b + (color2.b - color1.b) * t;
  return result;
}

export function getColorFromPalette(palette: ColorPalette, t: number): Color {
  // Clamp t to [0, 1]
  t = Math.max(0, Math.min(1, t));
  
  // Scale to palette range
  const scaledT = t * (palette.colors.length - 1);
  const index = Math.floor(scaledT);
  const fraction = scaledT - index;
  
  if (index >= palette.colors.length - 1) {
    return palette.colors[palette.colors.length - 1].clone();
  }
  
  return interpolateColor(palette.colors[index], palette.colors[index + 1], fraction);
}

// Shader-friendly palette data
export function createShaderPaletteData(): {
  paletteData: Float32Array;
  paletteSize: number;
  numPalettes: number;
} {
  const maxPaletteSize = Math.max(...PALETTES.map(p => p.colors.length));
  const paletteData = new Float32Array(PALETTES.length * maxPaletteSize * 3);
  
  PALETTES.forEach((palette, paletteIndex) => {
    palette.colors.forEach((color, colorIndex) => {
      const baseIndex = (paletteIndex * maxPaletteSize + colorIndex) * 3;
      paletteData[baseIndex] = color.r;
      paletteData[baseIndex + 1] = color.g;
      paletteData[baseIndex + 2] = color.b;
    });
    
    // Fill remaining slots with the last color to avoid artifacts
    for (let i = palette.colors.length; i < maxPaletteSize; i++) {
      const lastColor = palette.colors[palette.colors.length - 1];
      const baseIndex = (paletteIndex * maxPaletteSize + i) * 3;
      paletteData[baseIndex] = lastColor.r;
      paletteData[baseIndex + 1] = lastColor.g;
      paletteData[baseIndex + 2] = lastColor.b;
    }
  });
  
  return {
    paletteData,
    paletteSize: maxPaletteSize,
    numPalettes: PALETTES.length
  };
}
