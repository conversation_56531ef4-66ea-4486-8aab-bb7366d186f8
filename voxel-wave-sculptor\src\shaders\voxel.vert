// Vertex shader for voxel wave displacement
attribute vec3 aBasePos;
attribute vec3 aNormal;
attribute float aPhaseJitter;

uniform float time;
uniform float speed;
uniform float amplitude;
uniform float frequency;
uniform float complexity;
uniform int uSurfaceId;

varying vec3 vPosition;
varying vec3 vNormal;
varying float vWaveHeight;
varying float vPhase;

// Surface transformation functions
vec3 applySurfaceTransform(vec3 basePos, int surfaceId) {
    vec3 worldPos = basePos;
    
    if (surfaceId == 1) {
        // Cylindrical strip (radius = 2.0)
        float radius = 2.0;
        float angle = basePos.x * 3.14159; // Map x to angle
        worldPos.x = radius * cos(angle);
        worldPos.z = radius * sin(angle);
        worldPos.y = basePos.y;
    }
    else if (surfaceId == 2) {
        // Twisted band (Mobius-like with 0.5 twist)
        float radius = 2.0;
        float angle = basePos.x * 3.14159;
        float twist = basePos.x * 0.5; // 0.5 twist
        
        worldPos.x = (radius + basePos.y * cos(twist)) * cos(angle);
        worldPos.z = (radius + basePos.y * cos(twist)) * sin(angle);
        worldPos.y = basePos.y * sin(twist);
    }
    // surfaceId == 0: Plane - no transformation needed
    
    return worldPos;
}

// Wave displacement calculation
float calculateWaveDisplacement(vec2 flatPos, float time) {
    float x = flatPos.x;
    float y = flatPos.y;
    
    // Main wave equation as specified
    float wave1 = sin(frequency * sqrt(x*x + y*y) + time * speed);
    float wave2 = cos(complexity * x * y + time * 0.3);
    
    return amplitude * wave1 * wave2;
}

void main() {
    // Get the base position (flattened surface coordinates)
    vec3 basePos = aBasePos;
    vec2 flatPos = basePos.xy; // Use original x,y for wave calculation
    
    // Apply surface transformation to get world position
    vec3 worldPos = applySurfaceTransform(basePos, uSurfaceId);
    
    // Calculate wave displacement using original flat coordinates
    float waveHeight = calculateWaveDisplacement(flatPos, time + aPhaseJitter);
    
    // Apply displacement along surface normal
    vec3 displacedPos = worldPos + aNormal * waveHeight;
    
    // Calculate wave phase for scaling
    float phase = frequency * sqrt(flatPos.x*flatPos.x + flatPos.y*flatPos.y) + time * speed + aPhaseJitter;
    float scaleY = 1.0 + 0.25 * smoothstep(-1.0, 1.0, sin(phase));
    
    // Apply instance transformation
    vec4 instancePos = instanceMatrix * vec4(position, 1.0);
    
    // Scale the voxel
    instancePos.y *= scaleY;
    
    // Translate to displaced position
    instancePos.xyz += displacedPos;
    
    // Final transformation
    vec4 mvPosition = modelViewMatrix * instancePos;
    gl_Position = projectionMatrix * mvPosition;
    
    // Pass data to fragment shader
    vPosition = displacedPos;
    vNormal = normalize((modelMatrix * vec4(aNormal, 0.0)).xyz);
    vWaveHeight = waveHeight;
    vPhase = phase;
}
