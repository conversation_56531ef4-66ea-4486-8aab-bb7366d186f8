import './style.css';
import { VoxelField, SurfaceType } from './voxelField';
import { HandTracker, type HandParams } from './hand';
import { UIManager } from './ui';

class VoxelWaveSculptor {
  private voxelField: VoxelField | null = null;
  private handTracker: HandTracker | null = null;
  private uiManager: UIManager;

  private isRunning = false;
  private isPaused = false;
  private lastTime = 0;
  private animationId: number | null = null;

  // Fallback mode when camera is not available
  private fallbackPaletteIndex = 0;

  constructor() {
    this.uiManager = new UIManager();
    this.setupEventHandlers();
    this.init();
  }

  private setupEventHandlers(): void {
    // UI event handlers
    this.uiManager.onCameraAllow = () => this.initializeCamera();
    this.uiManager.onCameraDecline = () => this.initializeFallbackMode();
    this.uiManager.onSurfaceChange = (index) => this.changeSurface(index);
    this.uiManager.onPaletteCycle = () => this.cyclePalette();
    this.uiManager.onPauseToggle = () => this.togglePause();
    this.uiManager.onStatsToggle = () => this.toggleStats();

    // Mouse fallback handlers
    this.uiManager.onMouseAmplitude = (amplitude) => this.updateAmplitude(amplitude);
    this.uiManager.onMouseFrequency = (frequency) => this.updateFrequency(frequency);
  }

  private async init(): Promise<void> {
    try {
      this.uiManager.showLoading('Initializing Voxel Wave Sculptor...');

      // Initialize Three.js scene
      const canvas = document.getElementById('canvas') as HTMLCanvasElement;
      if (!canvas) {
        throw new Error('Canvas element not found');
      }

      this.voxelField = new VoxelField(canvas);

      // Show camera permission modal
      this.uiManager.hideLoading();
      this.uiManager.showCameraModal();

    } catch (error) {
      console.error('Initialization failed:', error);
      this.uiManager.hideLoading();
      this.uiManager.showError('Failed to initialize application. Please refresh and try again.');
    }
  }

  private async initializeCamera(): Promise<void> {
    try {
      this.uiManager.showLoading('Initializing camera and hand tracking...');

      // Initialize hand tracker
      this.handTracker = new HandTracker();

      // Set up hand parameter updates
      this.handTracker.onParamsUpdate((params: HandParams) => {
        this.updateFromHandParams(params);
      });

      // Start camera
      await this.handTracker.startCamera();

      this.uiManager.hideLoading();
      this.startRenderLoop();

    } catch (error) {
      console.error('Camera initialization failed:', error);
      this.uiManager.hideLoading();
      this.uiManager.showError('Camera access failed. Falling back to mouse controls.');
      this.initializeFallbackMode();
    }
  }

  private initializeFallbackMode(): void {
    this.uiManager.setupMouseFallback();
    this.uiManager.hideLoading();
    this.startRenderLoop();
  }

  private updateFromHandParams(params: HandParams): void {
    if (!this.voxelField) return;

    // Update voxel field parameters
    this.voxelField.updateParams({
      amplitude: params.amplitude,
      frequency: params.frequency,
      paletteIndex: params.paletteIndex
    });

    // Update UI
    this.uiManager.updateAmplitude(params.amplitude);
    this.uiManager.updateFrequency(params.frequency);
    this.uiManager.updatePalette(params.paletteIndex);

    // Update gesture status indicators
    this.uiManager.updateGestureStatus({
      pinch: params.amplitude > 0.1,
      wrist: Math.abs(params.frequency - 3.4) > 0.5, // Middle frequency is ~3.4
      flip: false, // Will be set during flip detection
      handDetected: params.handDetected
    });
  }

  private updateAmplitude(amplitude: number): void {
    if (this.voxelField) {
      this.voxelField.updateParams({ amplitude });
      this.uiManager.updateAmplitude(amplitude);
    }
  }

  private updateFrequency(frequency: number): void {
    if (this.voxelField) {
      this.voxelField.updateParams({ frequency });
      this.uiManager.updateFrequency(frequency);
    }
  }

  private changeSurface(index: number): void {
    if (this.voxelField) {
      this.voxelField.setSurface(index as SurfaceType);
      this.uiManager.updateSurface(index);
    }
  }

  private cyclePalette(): void {
    if (this.handTracker) {
      this.handTracker.cyclePalette();
    } else if (this.voxelField) {
      // Fallback palette cycling
      this.fallbackPaletteIndex = (this.fallbackPaletteIndex + 1) % 6;
      this.voxelField.updateParams({ paletteIndex: this.fallbackPaletteIndex });
      this.uiManager.updatePalette(this.fallbackPaletteIndex);
    }
  }

  private togglePause(): void {
    this.isPaused = !this.isPaused;
  }

  private toggleStats(): void {
    // Toggle stats display (could integrate stats.js here)
    console.log('Stats toggle requested');
  }

  private startRenderLoop(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.lastTime = performance.now();
    this.render();
  }

  private render = (): void => {
    if (!this.isRunning || !this.voxelField) return;

    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
    this.lastTime = currentTime;

    if (!this.isPaused) {
      // Render the scene
      this.voxelField.render(deltaTime);

      // Update FPS display
      this.uiManager.updateFPS(this.voxelField.getFPS());
    }

    // Continue render loop
    this.animationId = requestAnimationFrame(this.render);
  };

  // Cleanup
  dispose(): void {
    this.isRunning = false;

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.handTracker) {
      this.handTracker.stop();
    }

    if (this.voxelField) {
      this.voxelField.dispose();
    }
  }
}

// Initialize the application
const app = new VoxelWaveSculptor();

// Handle page unload
window.addEventListener('beforeunload', () => {
  app.dispose();
});
