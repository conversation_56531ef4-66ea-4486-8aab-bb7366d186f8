// Fragment shader for voxel coloring
precision highp float;

uniform float time;
uniform int paletteIndex;
uniform sampler2D uPaletteTexture;
uniform float uPaletteSize;
uniform float uNumPalettes;

varying vec3 vPosition;
varying vec3 vNormal;
varying float vWaveHeight;
varying float vPhase;

// Lighting uniforms
uniform vec3 uLightDirection;
uniform vec3 uLightColor;
uniform vec3 uAmbientColor;

// Get color from palette texture
vec3 getPaletteColor(int palette, float t) {
    // Clamp t to [0, 1]
    t = clamp(t, 0.0, 1.0);
    
    // Calculate texture coordinates
    float colorIndex = t * (uPaletteSize - 1.0);
    float u = (float(palette) + 0.5) / uNumPalettes;
    float v = (colorIndex + 0.5) / uPaletteSize;
    
    return texture2D(uPaletteTexture, vec2(v, u)).rgb;
}

// Quantize value for discrete color steps
float quantize(float value, float steps) {
    return floor(value * steps) / steps;
}

void main() {
    // Normalize wave height to [0, 1] for color mapping
    float normalizedHeight = (vWaveHeight + 1.0) * 0.5;
    
    // Add temporal hue shift
    float timeShift = sin(time * 0.5) * 0.1;
    float colorT = clamp(normalizedHeight + timeShift, 0.0, 1.0);
    
    // Quantize for discrete color steps (8-16 levels)
    colorT = quantize(colorT, 12.0);
    
    // Get base color from palette
    vec3 baseColor = getPaletteColor(paletteIndex, colorT);
    
    // Calculate lighting
    vec3 normal = normalize(vNormal);
    vec3 lightDir = normalize(-uLightDirection);
    
    // Diffuse lighting
    float diffuse = max(dot(normal, lightDir), 0.0);
    
    // Add some rim lighting based on wave phase
    float rim = 1.0 - abs(dot(normal, vec3(0.0, 0.0, 1.0)));
    rim = pow(rim, 2.0) * 0.3;
    
    // Combine lighting
    vec3 ambient = uAmbientColor * baseColor;
    vec3 diffuseColor = uLightColor * baseColor * diffuse;
    vec3 rimColor = baseColor * rim;
    
    vec3 finalColor = ambient + diffuseColor + rimColor;
    
    // Add subtle wave-based brightness variation
    float waveBrightness = 1.0 + sin(vPhase) * 0.1;
    finalColor *= waveBrightness;
    
    // Tone mapping (simple Reinhard)
    finalColor = finalColor / (finalColor + vec3(1.0));
    
    // Gamma correction
    finalColor = pow(finalColor, vec3(1.0 / 2.2));
    
    gl_FragColor = vec4(finalColor, 1.0);
}
