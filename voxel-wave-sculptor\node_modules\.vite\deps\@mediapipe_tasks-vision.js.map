{"version": 3, "sources": ["../../@mediapipe/tasks-vision/vision_bundle.mjs"], "sourcesContent": ["var t=\"undefined\"!=typeof self?self:{};function e(){throw Error(\"Invalid UTF8\")}function n(t,e){return e=String.fromCharCode.apply(null,e),null==t?e:t+e}let r,i;const s=\"undefined\"!=typeof TextDecoder;let o;const a=\"undefined\"!=typeof TextEncoder;function c(t){if(a)t=(o||=new TextEncoder).encode(t);else{let n=0;const r=new Uint8Array(3*t.length);for(let i=0;i<t.length;i++){var e=t.charCodeAt(i);if(e<128)r[n++]=e;else{if(e<2048)r[n++]=e>>6|192;else{if(e>=55296&&e<=57343){if(e<=56319&&i<t.length){const s=t.charCodeAt(++i);if(s>=56320&&s<=57343){e=1024*(e-55296)+s-56320+65536,r[n++]=e>>18|240,r[n++]=e>>12&63|128,r[n++]=e>>6&63|128,r[n++]=63&e|128;continue}i--}e=65533}r[n++]=e>>12|224,r[n++]=e>>6&63|128}r[n++]=63&e|128}}t=n===r.length?r:r.subarray(0,n)}return t}var h,u;t:{for(var l=[\"CLOSURE_FLAGS\"],d=t,f=0;f<l.length;f++)if(null==(d=d[l[f]])){u=null;break t}u=d}var p,g=u&&u[610401301];h=null!=g&&g;const m=t.navigator;function y(t){return!!h&&(!!p&&p.brands.some((({brand:e})=>e&&-1!=e.indexOf(t))))}function _(e){var n;return(n=t.navigator)&&(n=n.userAgent)||(n=\"\"),-1!=n.indexOf(e)}function v(){return!!h&&(!!p&&p.brands.length>0)}function E(){return v()?y(\"Chromium\"):(_(\"Chrome\")||_(\"CriOS\"))&&!(!v()&&_(\"Edge\"))||_(\"Silk\")}function w(t){return w[\" \"](t),t}p=m&&m.userAgentData||null,w[\" \"]=function(){};var T=!v()&&(_(\"Trident\")||_(\"MSIE\"));!_(\"Android\")||E(),E(),_(\"Safari\")&&(E()||!v()&&_(\"Coast\")||!v()&&_(\"Opera\")||!v()&&_(\"Edge\")||(v()?y(\"Microsoft Edge\"):_(\"Edg/\"))||v()&&y(\"Opera\"));var A={},b=null;function k(t){const e=t.length;let n=3*e/4;n%3?n=Math.floor(n):-1!=\"=.\".indexOf(t[e-1])&&(n=-1!=\"=.\".indexOf(t[e-2])?n-2:n-1);const r=new Uint8Array(n);let i=0;return function(t,e){function n(e){for(;r<t.length;){const e=t.charAt(r++),n=b[e];if(null!=n)return n;if(!/^[\\s\\xa0]*$/.test(e))throw Error(\"Unknown base64 encoding at char: \"+e)}return e}S();let r=0;for(;;){const t=n(-1),r=n(0),i=n(64),s=n(64);if(64===s&&-1===t)break;e(t<<2|r>>4),64!=i&&(e(r<<4&240|i>>2),64!=s&&e(i<<6&192|s))}}(t,(function(t){r[i++]=t})),i!==n?r.subarray(0,i):r}function S(){if(!b){b={};var t=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\"),e=[\"+/=\",\"+/\",\"-_=\",\"-_.\",\"-_\"];for(let n=0;n<5;n++){const r=t.concat(e[n].split(\"\"));A[n]=r;for(let t=0;t<r.length;t++){const e=r[t];void 0===b[e]&&(b[e]=t)}}}}var x=\"undefined\"!=typeof Uint8Array,L=!T&&\"function\"==typeof btoa;function R(t){if(!L){var e;void 0===e&&(e=0),S(),e=A[e];var n=Array(Math.floor(t.length/3)),r=e[64]||\"\";let c=0,h=0;for(;c<t.length-2;c+=3){var i=t[c],s=t[c+1],o=t[c+2],a=e[i>>2];i=e[(3&i)<<4|s>>4],s=e[(15&s)<<2|o>>6],o=e[63&o],n[h++]=a+i+s+o}switch(a=0,o=r,t.length-c){case 2:o=e[(15&(a=t[c+1]))<<2]||r;case 1:t=t[c],n[h]=e[t>>2]+e[(3&t)<<4|a>>4]+o+r}return n.join(\"\")}for(e=\"\",n=0,r=t.length-10240;n<r;)e+=String.fromCharCode.apply(null,t.subarray(n,n+=10240));return e+=String.fromCharCode.apply(null,n?t.subarray(n):t),btoa(e)}const F=/[-_.]/g,I={\"-\":\"+\",_:\"/\",\".\":\"=\"};function M(t){return I[t]||\"\"}function P(t){if(!L)return k(t);F.test(t)&&(t=t.replace(F,M)),t=atob(t);const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}function C(t){return x&&null!=t&&t instanceof Uint8Array}var O={};function U(){return B||=new N(null,O)}function D(t){j(O);var e=t.g;return null==(e=null==e||C(e)?e:\"string\"==typeof e?P(e):null)?e:t.g=e}var N=class{h(){return new Uint8Array(D(this)||0)}constructor(t,e){if(j(e),this.g=t,null!=t&&0===t.length)throw Error(\"ByteString should be constructed with non-empty values\")}};let B,G;function j(t){if(t!==O)throw Error(\"illegal external caller\")}function V(t,e){t.__closure__error__context__984382||(t.__closure__error__context__984382={}),t.__closure__error__context__984382.severity=e}function X(t){return V(t=Error(t),\"warning\"),t}function H(e){if(null!=e){var n=G??={},r=n[e]||0;r>=5||(n[e]=r+1,V(e=Error(),\"incident\"),function(e){t.setTimeout((()=>{throw e}),0)}(e))}}var W=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol();function z(t,e,n=!1){return\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol()?n&&Symbol.for&&t?Symbol.for(t):null!=t?Symbol(t):Symbol():e}var K=z(\"jas\",void 0,!0),Y=z(void 0,\"0di\"),$=z(void 0,\"1oa\"),q=z(void 0,Symbol()),J=z(void 0,\"0actk\"),Z=z(void 0,\"8utk\");const Q=W?K:\"Ea\",tt={Ea:{value:0,configurable:!0,writable:!0,enumerable:!1}},et=Object.defineProperties;function nt(t,e){W||Q in t||et(t,tt),t[Q]|=e}function rt(t,e){W||Q in t||et(t,tt),t[Q]=e}function it(t){return nt(t,34),t}function st(t,e){rt(e,-15615&(0|t))}function ot(t,e){rt(e,-15581&(34|t))}function at(){return\"function\"==typeof BigInt}function ct(t){return Array.prototype.slice.call(t)}var ht,ut={};function lt(t){return null!==t&&\"object\"==typeof t&&!Array.isArray(t)&&t.constructor===Object}function dt(t,e){if(null!=t)if(\"string\"==typeof t)t=t?new N(t,O):U();else if(t.constructor!==N)if(C(t))t=t.length?new N(new Uint8Array(t),O):U();else{if(!e)throw Error();t=void 0}return t}const ft=[];function pt(t){if(2&t)throw Error()}rt(ft,55),ht=Object.freeze(ft);class gt{constructor(t,e,n){this.g=t,this.h=e,this.l=n}next(){const t=this.g.next();return t.done||(t.value=this.h.call(this.l,t.value)),t}[Symbol.iterator](){return this}}function mt(t){return q?t[q]:void 0}var yt=Object.freeze({});function _t(t){return t.Na=!0,t}var vt=_t((t=>\"number\"==typeof t)),Et=_t((t=>\"string\"==typeof t)),wt=_t((t=>\"boolean\"==typeof t)),Tt=\"function\"==typeof t.BigInt&&\"bigint\"==typeof t.BigInt(0);function At(t){var e=t;if(Et(e)){if(!/^\\s*(?:-?[1-9]\\d*|0)?\\s*$/.test(e))throw Error(String(e))}else if(vt(e)&&!Number.isSafeInteger(e))throw Error(String(e));return Tt?BigInt(t):t=wt(t)?t?\"1\":\"0\":Et(t)?t.trim()||\"0\":String(t)}var bt=_t((t=>Tt?t>=St&&t<=Lt:\"-\"===t[0]?Rt(t,kt):Rt(t,xt)));const kt=Number.MIN_SAFE_INTEGER.toString(),St=Tt?BigInt(Number.MIN_SAFE_INTEGER):void 0,xt=Number.MAX_SAFE_INTEGER.toString(),Lt=Tt?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Rt(t,e){if(t.length>e.length)return!1;if(t.length<e.length||t===e)return!0;for(let n=0;n<t.length;n++){const r=t[n],i=e[n];if(r>i)return!1;if(r<i)return!0}}const Ft=\"function\"==typeof Uint8Array.prototype.slice;let It,Mt=0,Pt=0;function Ct(t){const e=t>>>0;Mt=e,Pt=(t-e)/4294967296>>>0}function Ot(t){if(t<0){Ct(-t);const[e,n]=Xt(Mt,Pt);Mt=e>>>0,Pt=n>>>0}else Ct(t)}function Ut(t){const e=It||=new DataView(new ArrayBuffer(8));e.setFloat32(0,+t,!0),Pt=0,Mt=e.getUint32(0,!0)}function Dt(t,e){const n=4294967296*e+(t>>>0);return Number.isSafeInteger(n)?n:Bt(t,e)}function Nt(t,e){const n=2147483648&e;return n&&(e=~e>>>0,0==(t=1+~t>>>0)&&(e=e+1>>>0)),\"number\"==typeof(t=Dt(t,e))?n?-t:t:n?\"-\"+t:t}function Bt(t,e){if(t>>>=0,(e>>>=0)<=2097151)var n=\"\"+(4294967296*e+t);else at()?n=\"\"+(BigInt(e)<<BigInt(32)|BigInt(t)):(t=(16777215&t)+6777216*(n=16777215&(t>>>24|e<<8))+6710656*(e=e>>16&65535),n+=8147497*e,e*=2,t>=1e7&&(n+=t/1e7>>>0,t%=1e7),n>=1e7&&(e+=n/1e7>>>0,n%=1e7),n=e+Gt(n)+Gt(t));return n}function Gt(t){return t=String(t),\"0000000\".slice(t.length)+t}function jt(){var t=Mt,e=Pt;if(2147483648&e)if(at())t=\"\"+(BigInt(0|e)<<BigInt(32)|BigInt(t>>>0));else{const[n,r]=Xt(t,e);t=\"-\"+Bt(n,r)}else t=Bt(t,e);return t}function Vt(t){if(t.length<16)Ot(Number(t));else if(at())t=BigInt(t),Mt=Number(t&BigInt(4294967295))>>>0,Pt=Number(t>>BigInt(32)&BigInt(4294967295));else{const e=+(\"-\"===t[0]);Pt=Mt=0;const n=t.length;for(let r=e,i=(n-e)%6+e;i<=n;r=i,i+=6){const e=Number(t.slice(r,i));Pt*=1e6,Mt=1e6*Mt+e,Mt>=4294967296&&(Pt+=Math.trunc(Mt/4294967296),Pt>>>=0,Mt>>>=0)}if(e){const[t,e]=Xt(Mt,Pt);Mt=t,Pt=e}}}function Xt(t,e){return e=~e,t?t=1+~t:e+=1,[t,e]}const Ht=\"function\"==typeof BigInt?BigInt.asIntN:void 0,Wt=\"function\"==typeof BigInt?BigInt.asUintN:void 0,zt=Number.isSafeInteger,Kt=Number.isFinite,Yt=Math.trunc,$t=At(0);function qt(t){return null==t||\"number\"==typeof t?t:\"NaN\"===t||\"Infinity\"===t||\"-Infinity\"===t?Number(t):void 0}function Jt(t){return null==t||\"boolean\"==typeof t?t:\"number\"==typeof t?!!t:void 0}const Zt=/^-?([1-9][0-9]*|0)(\\.[0-9]+)?$/;function Qt(t){switch(typeof t){case\"bigint\":return!0;case\"number\":return Kt(t);case\"string\":return Zt.test(t);default:return!1}}function te(t){if(null==t)return t;if(\"string\"==typeof t&&t)t=+t;else if(\"number\"!=typeof t)return;return Kt(t)?0|t:void 0}function ee(t){if(null==t)return t;if(\"string\"==typeof t&&t)t=+t;else if(\"number\"!=typeof t)return;return Kt(t)?t>>>0:void 0}function ne(t){if(\"-\"===t[0])return!1;const e=t.length;return e<20||20===e&&Number(t.substring(0,6))<184467}function re(t){const e=t.length;return\"-\"===t[0]?e<20||20===e&&Number(t.substring(0,7))>-922337:e<19||19===e&&Number(t.substring(0,6))<922337}function ie(t){return re(t)?t:(Vt(t),jt())}function se(t){return t=Yt(t),zt(t)||(Ot(t),t=Nt(Mt,Pt)),t}function oe(t){var e=Yt(Number(t));return zt(e)?String(e):(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),ie(t))}function ae(t){var e=Yt(Number(t));return zt(e)?At(e):(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),at()?At(Ht(64,BigInt(t))):At(ie(t)))}function ce(t){if(zt(t))t=At(se(t));else{if(t=Yt(t),zt(t))t=String(t);else{const e=String(t);re(e)?t=e:(Ot(t),t=jt())}t=At(t)}return t}function he(t){return null==t?t:\"bigint\"==typeof t?(bt(t)?t=Number(t):(t=Ht(64,t),t=bt(t)?Number(t):String(t)),t):Qt(t)?\"number\"==typeof t?se(t):oe(t):void 0}function ue(t){if(null==t)return t;var e=typeof t;if(\"bigint\"===e)return String(Wt(64,t));if(Qt(t)){if(\"string\"===e)return e=Yt(Number(t)),zt(e)&&e>=0?t=String(e):(-1!==(e=t.indexOf(\".\"))&&(t=t.substring(0,e)),ne(t)||(Vt(t),t=Bt(Mt,Pt))),t;if(\"number\"===e)return(t=Yt(t))>=0&&zt(t)?t:function(t){if(t<0){Ot(t);var e=Bt(Mt,Pt);return t=Number(e),zt(t)?t:e}return ne(e=String(t))?e:(Ot(t),Dt(Mt,Pt))}(t)}}function le(t){if(\"string\"!=typeof t)throw Error();return t}function de(t){if(null!=t&&\"string\"!=typeof t)throw Error();return t}function fe(t){return null==t||\"string\"==typeof t?t:void 0}function pe(t,e,n,r){if(null!=t&&\"object\"==typeof t&&t.W===ut)return t;if(!Array.isArray(t))return n?2&r?((t=e[Y])||(it((t=new e).u),t=e[Y]=t),e=t):e=new e:e=void 0,e;let i=n=0|t[Q];return 0===i&&(i|=32&r),i|=2&r,i!==n&&rt(t,i),new e(t)}function ge(t,e,n){if(e)t:{if(!Qt(e=t))throw X(\"int64\");switch(typeof e){case\"string\":e=ae(e);break t;case\"bigint\":e=At(Ht(64,e));break t;default:e=ce(e)}}else t=typeof(e=t),e=null==e?e:\"bigint\"===t?At(Ht(64,e)):Qt(e)?\"string\"===t?ae(e):ce(e):void 0;return null==(t=e)?n?$t:void 0:t}function me(t){return t}const ye={};let _e=function(){try{return w(new class extends Map{constructor(){super()}}),!1}catch{return!0}}();class ve{constructor(){this.g=new Map}get(t){return this.g.get(t)}set(t,e){return this.g.set(t,e),this.size=this.g.size,this}delete(t){return t=this.g.delete(t),this.size=this.g.size,t}clear(){this.g.clear(),this.size=this.g.size}has(t){return this.g.has(t)}entries(){return this.g.entries()}keys(){return this.g.keys()}values(){return this.g.values()}forEach(t,e){return this.g.forEach(t,e)}[Symbol.iterator](){return this.entries()}}const Ee=_e?(Object.setPrototypeOf(ve.prototype,Map.prototype),Object.defineProperties(ve.prototype,{size:{value:0,configurable:!0,enumerable:!0,writable:!0}}),ve):class extends Map{constructor(){super()}};function we(t){return t}function Te(t){if(2&t.M)throw Error(\"Cannot mutate an immutable Map\")}var Ae=class extends Ee{constructor(t,e,n=we,r=we){super();let i=0|t[Q];i|=64,rt(t,i),this.M=i,this.I=e,this.S=n,this.X=this.I?be:r;for(let s=0;s<t.length;s++){const o=t[s],a=n(o[0],!1,!0);let c=o[1];e?void 0===c&&(c=null):c=r(o[1],!1,!0,void 0,void 0,i),super.set(a,c)}}La(){var t=Ce;if(0!==this.size)return Array.from(super.entries(),(e=>(e[0]=t(e[0]),e[1]=t(e[1]),e)))}da(t=ke){const e=[],n=super.entries();for(var r;!(r=n.next()).done;)(r=r.value)[0]=t(r[0]),r[1]=t(r[1]),e.push(r);return e}clear(){Te(this),super.clear()}delete(t){return Te(this),super.delete(this.S(t,!0,!1))}entries(){if(this.I){var t=super.keys();t=new gt(t,Se,this)}else t=super.entries();return t}values(){if(this.I){var t=super.keys();t=new gt(t,Ae.prototype.get,this)}else t=super.values();return t}forEach(t,e){this.I?super.forEach(((n,r,i)=>{t.call(e,i.get(r),r,i)})):super.forEach(t,e)}set(t,e){return Te(this),null==(t=this.S(t,!0,!1))?this:null==e?(super.delete(t),this):super.set(t,this.X(e,!0,!0,this.I,!1,this.M))}Ja(t){const e=this.S(t[0],!1,!0);t=t[1],t=this.I?void 0===t?null:t:this.X(t,!1,!0,void 0,!1,this.M),super.set(e,t)}has(t){return super.has(this.S(t,!1,!1))}get(t){t=this.S(t,!1,!1);const e=super.get(t);if(void 0!==e){var n=this.I;return n?((n=this.X(e,!1,!0,n,this.pa,this.M))!==e&&super.set(t,n),n):e}}[Symbol.iterator](){return this.entries()}};function be(t,e,n,r,i,s){return t=pe(t,r,n,s),i&&(t=je(t)),t}function ke(t){return t}function Se(t){return[t,this.get(t)]}let xe,Le,Re,Fe;function Ie(){return xe||=new Ae(it([]),void 0,void 0,void 0,ye)}function Me(t,e,n,r,i){if(null!=t){if(Array.isArray(t)){const s=0|t[Q];return 0===t.length&&1&s?void 0:i&&2&s?t:Pe(t,e,n,void 0!==r,i)}return e(t,r)}}function Pe(t,e,n,r,i){const s=r||n?0|t[Q]:0,o=r?!!(32&s):void 0;let a=0;const c=(r=ct(t)).length;for(let t=0;t<c;t++){var h=r[t];if(t===c-1&&lt(h)){var u=e,l=n,d=o,f=i;let t;for(let e in h){const n=Me(h[e],u,l,d,f);null!=n&&((t??={})[e]=n)}h=t}else h=Me(r[t],e,n,o,i);r[t]=h,null!=h&&(a=t+1)}return a<c&&(r.length=a),n&&((t=mt(t))&&(r[q]=ct(t)),n(s,r)),r}function Ce(t){return Me(t,Oe,void 0,void 0,!1)}function Oe(t){switch(typeof t){case\"number\":return Number.isFinite(t)?t:\"\"+t;case\"bigint\":return bt(t)?Number(t):\"\"+t;case\"boolean\":return t?1:0;case\"object\":if(C(t))return C(t)&&H(Z),R(t);if(t.W===ut)return Ue(t);if(t instanceof N){const e=t.g;return null==e?\"\":\"string\"==typeof e?e:t.g=R(e)}return t instanceof Ae?t.La():void 0}return t}function Ue(t){var e=t.u;t=Pe(e,Oe,void 0,void 0,!1);var n=0|e[Q];if((e=t.length)&&!(512&n)){var r=t[e-1],i=!1;lt(r)?(e--,i=!0):r=void 0;var s=e-(n=512&n?0:-1),o=(Le??me)(s,n,t,r);if(r&&(t[e]=void 0),s<o&&r){for(var a in s=!0,r){const c=+a;c<=o?(t[i=c+n]=r[a],e=Math.max(i+1,e),i=!1,delete r[a]):s=!1}s&&(r=void 0)}for(s=e-1;e>0;s=e-1)if(null==(a=t[s]))e--,i=!0;else{if(!((s-=n)>=o))break;(r??={})[s]=a,e--,i=!0}i&&(t.length=e),r&&t.push(r)}return t}function De(t,e,n){return t=Ne(t,e[0],e[1],n?1:2),e!==Re&&n&&nt(t,8192),t}function Ne(t,e,n,r){if(null==t){var i=96;n?(t=[n],i|=512):t=[],e&&(i=-16760833&i|(1023&e)<<14)}else{if(!Array.isArray(t))throw Error(\"narr\");if(8192&(i=0|t[Q])||!(64&i)||2&i||H(J),1024&i)throw Error(\"farr\");if(64&i)return t;if(1===r||2===r||(i|=64),n&&(i|=512,n!==t[0]))throw Error(\"mid\");t:{var s=(n=t).length;if(s){var o=s-1;if(lt(r=n[o])){if((o-=e=512&(i|=256)?0:-1)>=1024)throw Error(\"pvtlmt\");for(var a in r)(s=+a)<o&&(n[s+e]=r[a],delete r[a]);i=-16760833&i|(1023&o)<<14;break t}}if(e){if((a=Math.max(e,s-(512&i?0:-1)))>1024)throw Error(\"spvt\");i=-16760833&i|(1023&a)<<14}}}return rt(t,i),t}function Be(t,e,n=ot){if(null!=t){if(x&&t instanceof Uint8Array)return e?t:new Uint8Array(t);if(Array.isArray(t)){var r=0|t[Q];return 2&r?t:(e&&=0===r||!!(32&r)&&!(64&r||!(16&r)),e?(rt(t,34|r),4&r&&Object.freeze(t),t):Pe(t,Be,4&r?ot:n,!0,!0))}return t.W===ut?t=2&(r=0|(n=t.u)[Q])?t:new t.constructor(Ge(n,r,!0)):t instanceof Ae&&!(2&t.M)&&(n=it(t.da(Be)),t=new Ae(n,t.I,t.S,t.X)),t}}function Ge(t,e,n){const r=n||2&e?ot:st,i=!!(32&e);return t=function(t,e,n){const r=ct(t);var i=r.length;const s=256&e?r[i-1]:void 0;for(i+=s?-1:0,e=512&e?1:0;e<i;e++)r[e]=n(r[e]);if(s){e=r[e]={};for(const t in s)e[t]=n(s[t])}return(t=mt(t))&&(r[q]=ct(t)),r}(t,e,(t=>Be(t,i,r))),nt(t,32|(n?2:0)),t}function je(t){const e=t.u,n=0|e[Q];return 2&n?new t.constructor(Ge(e,n,!1)):t}function Ve(t,e){return Xe(t=t.u,0|t[Q],e)}function Xe(t,e,n){if(-1===n)return null;const r=n+(512&e?0:-1),i=t.length-1;return r>=i&&256&e?t[i][n]:r<=i?t[r]:void 0}function He(t,e,n){const r=t.u;let i=0|r[Q];return pt(i),We(r,i,e,n),t}function We(t,e,n,r){const i=512&e?0:-1,s=n+i;var o=t.length-1;return s>=o&&256&e?(t[o][n]=r,e):s<=o?(t[s]=r,e):(void 0!==r&&(n>=(o=e>>14&1023||536870912)?null!=r&&(t[o+i]={[n]:r},rt(t,e|=256)):t[s]=r),e)}function ze(t,e){let n=0|(t=t.u)[Q];const r=Xe(t,n,e),i=qt(r);return null!=i&&i!==r&&We(t,n,e,i),i}function Ke(t){let e=0|(t=t.u)[Q];const n=Xe(t,e,1),r=dt(n,!0);return null!=r&&r!==n&&We(t,e,1,r),r}function Ye(){return void 0===yt?2:4}function $e(t,e,n,r,i){const s=t.u,o=2&(t=0|s[Q])?1:r;i=!!i;let a=0|(r=qe(s,t,e))[Q];if(!(4&a)){4&a&&(r=ct(r),a=pn(a,t),t=We(s,t,e,r));let i=0,o=0;for(;i<r.length;i++){const t=n(r[i]);null!=t&&(r[o++]=t)}o<i&&(r.length=o),a=Je(a,t),n=-2049&(20|a),a=n&=-4097,rt(r,a),2&a&&Object.freeze(r)}return 1===o||4===o&&32&a?Ze(a)||(i=a,a|=2,a!==i&&rt(r,a),Object.freeze(r)):(2===o&&Ze(a)&&(r=ct(r),a=pn(a,t),a=gn(a,t,i),rt(r,a),t=We(s,t,e,r)),Ze(a)||(e=a,a=gn(a,t,i),a!==e&&rt(r,a))),r}function qe(t,e,n){return t=Xe(t,e,n),Array.isArray(t)?t:ht}function Je(t,e){return 0===t&&(t=pn(t,e)),1|t}function Ze(t){return!!(2&t)&&!!(4&t)||!!(1024&t)}function Qe(t){t=ct(t);for(let e=0;e<t.length;e++){const n=t[e]=ct(t[e]);Array.isArray(n[1])&&(n[1]=it(n[1]))}return t}function tn(t,e,n,r){let i=0|(t=t.u)[Q];pt(i),We(t,i,e,(\"0\"===r?0===Number(n):n===r)?void 0:n)}function en(t,e,n,r){pt(e);let i=qe(t,e,n);const s=i!==ht;if(64&e||!(8192&e)||!s){const o=s?0|i[Q]:0;let a=o;(!s||2&a||Ze(a)||4&a&&!(32&a))&&(i=ct(i),a=pn(a,e),e=We(t,e,n,i)),a=-13&Je(a,e),a=gn(r?-17&a:16|a,e,!0),a!==o&&rt(i,a)}return i}function nn(t,e){var n=Ts;return on(rn(t=t.u),t,0|t[Q],n)===e?e:-1}function rn(t){if(W)return t[$]??(t[$]=new Map);if($ in t)return t[$];const e=new Map;return Object.defineProperty(t,$,{value:e}),e}function sn(t,e,n,r){const i=rn(t),s=on(i,t,e,n);return s!==r&&(s&&(e=We(t,e,s)),i.set(n,r)),e}function on(t,e,n,r){let i=t.get(r);if(null!=i)return i;i=0;for(let t=0;t<r.length;t++){const s=r[t];null!=Xe(e,n,s)&&(0!==i&&(n=We(e,n,i)),i=s)}return t.set(r,i),i}function an(t,e,n){let r=0|t[Q];const i=Xe(t,r,n);let s;if(null!=i&&i.W===ut)return(e=je(i))!==i&&We(t,r,n,e),e.u;if(Array.isArray(i)){const t=0|i[Q];s=2&t?De(Ge(i,t,!1),e,!0):64&t?i:De(s,e,!0)}else s=De(void 0,e,!0);return s!==i&&We(t,r,n,s),s}function cn(t,e,n){let r=0|(t=t.u)[Q];const i=Xe(t,r,n);return(e=pe(i,e,!1,r))!==i&&null!=e&&We(t,r,n,e),e}function hn(t,e,n){if(null==(e=cn(t,e,n)))return e;let r=0|(t=t.u)[Q];if(!(2&r)){const i=je(e);i!==e&&We(t,r,n,e=i)}return e}function un(t,e,n,r,i,s,o){t=t.u;var a=!!(2&e);const c=a?1:i;s=!!s,o&&=!a;var h=0|(i=qe(t,e,r))[Q];if(!(a=!!(4&h))){var u=i,l=e;const t=!!(2&(h=Je(h,e)));t&&(l|=2);let r=!t,s=!0,o=0,a=0;for(;o<u.length;o++){const e=pe(u[o],n,!1,l);if(e instanceof n){if(!t){const t=!!(2&(0|e.u[Q]));r&&=!t,s&&=t}u[a++]=e}}a<o&&(u.length=a),h|=4,h=s?16|h:-17&h,rt(u,h=r?8|h:-9&h),t&&Object.freeze(u)}if(o&&!(8&h||!i.length&&(1===c||4===c&&32&h))){for(Ze(h)&&(i=ct(i),h=pn(h,e),e=We(t,e,r,i)),n=i,o=h,u=0;u<n.length;u++)(h=n[u])!==(l=je(h))&&(n[u]=l);o|=8,rt(n,o=n.length?-17&o:16|o),h=o}return 1===c||4===c&&32&h?Ze(h)||(e=h,(h|=!i.length||16&h&&(!a||32&h)?2:1024)!==e&&rt(i,h),Object.freeze(i)):(2===c&&Ze(h)&&(rt(i=ct(i),h=gn(h=pn(h,e),e,s)),e=We(t,e,r,i)),Ze(h)||(r=h,(h=gn(h,e,s))!==r&&rt(i,h))),i}function ln(t,e,n){const r=0|t.u[Q];return un(t,r,e,n,Ye(),!1,!(2&r))}function dn(t,e,n,r){return null==r&&(r=void 0),He(t,n,r)}function fn(t,e,n,r){null==r&&(r=void 0);t:{let i=0|(t=t.u)[Q];if(pt(i),null==r){const r=rn(t);if(on(r,t,i,n)!==e)break t;r.set(n,0)}else i=sn(t,i,n,e);We(t,i,e,r)}}function pn(t,e){return-1025&(t=32|(2&e?2|t:-3&t))}function gn(t,e,n){return 32&e&&n||(t&=-33),t}function mn(t,e,n){pt(0|t.u[Q]),$e(t,e,fe,2,!0).push(le(n))}function yn(t,e,n,r){const i=0|t.u[Q];pt(i),t=un(t,i,n,e,2,!0),r=null!=r?r:new n,t.push(r),t[Q]=2&(0|r.u[Q])?-9&t[Q]:-17&t[Q]}function _n(t,e){return te(Ve(t,e))}function vn(t,e){return fe(Ve(t,e))}function En(t,e){return ze(t,e)??0}function wn(t,e,n){if(null!=n&&\"boolean\"!=typeof n)throw t=typeof n,Error(`Expected boolean but got ${\"object\"!=t?t:n?Array.isArray(n)?\"array\":t:\"null\"}: ${n}`);He(t,e,n)}function Tn(t,e,n){if(null!=n){if(\"number\"!=typeof n)throw X(\"int32\");if(!Kt(n))throw X(\"int32\");n|=0}He(t,e,n)}function An(t,e,n){if(null!=n&&\"number\"!=typeof n)throw Error(`Value of float/double field must be a number, found ${typeof n}: ${n}`);He(t,e,n)}function bn(t,e,n){{const o=t.u;let a=0|o[Q];if(pt(a),null==n)We(o,a,e);else{var r=t=0|n[Q],i=Ze(t),s=i||Object.isFrozen(n);for(i||(t=0),s||(n=ct(n),r=0,t=gn(t=pn(t,a),a,!0),s=!1),t|=21,i=0;i<n.length;i++){const e=n[i],o=le(e);Object.is(e,o)||(s&&(n=ct(n),r=0,t=gn(t=pn(t,a),a,!0),s=!1),n[i]=o)}t!==r&&(s&&(n=ct(n),t=gn(t=pn(t,a),a,!0)),rt(n,t)),We(o,a,e,n)}}}function kn(t,e){return Error(`Invalid wire type: ${t} (at position ${e})`)}function Sn(){return Error(\"Failed to read varint, encoding is invalid.\")}function xn(t,e){return Error(`Tried to read past the end of the data ${e} > ${t}`)}function Ln(t){if(\"string\"==typeof t)return{buffer:P(t),O:!1};if(Array.isArray(t))return{buffer:new Uint8Array(t),O:!1};if(t.constructor===Uint8Array)return{buffer:t,O:!1};if(t.constructor===ArrayBuffer)return{buffer:new Uint8Array(t),O:!1};if(t.constructor===N)return{buffer:D(t)||new Uint8Array(0),O:!0};if(t instanceof Uint8Array)return{buffer:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),O:!1};throw Error(\"Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers\")}function Rn(t,e){let n,r=0,i=0,s=0;const o=t.h;let a=t.g;do{n=o[a++],r|=(127&n)<<s,s+=7}while(s<32&&128&n);for(s>32&&(i|=(127&n)>>4),s=3;s<32&&128&n;s+=7)n=o[a++],i|=(127&n)<<s;if(Dn(t,a),n<128)return e(r>>>0,i>>>0);throw Sn()}function Fn(t){let e=0,n=t.g;const r=n+10,i=t.h;for(;n<r;){const r=i[n++];if(e|=r,0==(128&r))return Dn(t,n),!!(127&e)}throw Sn()}function In(t){const e=t.h;let n=t.g,r=e[n++],i=127&r;if(128&r&&(r=e[n++],i|=(127&r)<<7,128&r&&(r=e[n++],i|=(127&r)<<14,128&r&&(r=e[n++],i|=(127&r)<<21,128&r&&(r=e[n++],i|=r<<28,128&r&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++])))))throw Sn();return Dn(t,n),i}function Mn(t){return In(t)>>>0}function Pn(t){var e=t.h;const n=t.g,r=e[n],i=e[n+1],s=e[n+2];return e=e[n+3],Dn(t,t.g+4),(r<<0|i<<8|s<<16|e<<24)>>>0}function Cn(t){var e=Pn(t);t=2*(e>>31)+1;const n=e>>>23&255;return e&=8388607,255==n?e?NaN:t*(1/0):0==n?1401298464324817e-60*t*e:t*Math.pow(2,n-150)*(e+8388608)}function On(t){return In(t)}function Un(t,e,{aa:n=!1}={}){t.aa=n,e&&(e=Ln(e),t.h=e.buffer,t.m=e.O,t.j=0,t.l=t.h.length,t.g=t.j)}function Dn(t,e){if(t.g=e,e>t.l)throw xn(t.l,e)}function Nn(t,e){if(e<0)throw Error(`Tried to read a negative byte length: ${e}`);const n=t.g,r=n+e;if(r>t.l)throw xn(e,t.l-n);return t.g=r,n}function Bn(t,e){if(0==e)return U();var n=Nn(t,e);return t.aa&&t.m?n=t.h.subarray(n,n+e):(t=t.h,n=n===(e=n+e)?new Uint8Array(0):Ft?t.slice(n,e):new Uint8Array(t.subarray(n,e))),0==n.length?U():new N(n,O)}Ae.prototype.toJSON=void 0;var Gn=[];function jn(t){var e=t.g;if(e.g==e.l)return!1;t.l=t.g.g;var n=Mn(t.g);if(e=n>>>3,!((n&=7)>=0&&n<=5))throw kn(n,t.l);if(e<1)throw Error(`Invalid field number: ${e} (at position ${t.l})`);return t.m=e,t.h=n,!0}function Vn(t){switch(t.h){case 0:0!=t.h?Vn(t):Fn(t.g);break;case 1:Dn(t=t.g,t.g+8);break;case 2:if(2!=t.h)Vn(t);else{var e=Mn(t.g);Dn(t=t.g,t.g+e)}break;case 5:Dn(t=t.g,t.g+4);break;case 3:for(e=t.m;;){if(!jn(t))throw Error(\"Unmatched start-group tag: stream EOF\");if(4==t.h){if(t.m!=e)throw Error(\"Unmatched end-group tag\");break}Vn(t)}break;default:throw kn(t.h,t.l)}}function Xn(t,e,n){const r=t.g.l,i=Mn(t.g),s=t.g.g+i;let o=s-r;if(o<=0&&(t.g.l=s,n(e,t,void 0,void 0,void 0),o=s-t.g.g),o)throw Error(`Message parsing ended unexpectedly. Expected to read ${i} bytes, instead read ${i-o} bytes, either the data ended unexpectedly or the message misreported its own length`);return t.g.g=s,t.g.l=r,e}function Hn(t){var o=Mn(t.g),a=Nn(t=t.g,o);if(t=t.h,s){var c,h=t;(c=i)||(c=i=new TextDecoder(\"utf-8\",{fatal:!0})),o=a+o,h=0===a&&o===h.length?h:h.subarray(a,o);try{var u=c.decode(h)}catch(t){if(void 0===r){try{c.decode(new Uint8Array([128]))}catch(t){}try{c.decode(new Uint8Array([97])),r=!0}catch(t){r=!1}}throw!r&&(i=void 0),t}}else{o=(u=a)+o,a=[];let r,i=null;for(;u<o;){var l=t[u++];l<128?a.push(l):l<224?u>=o?e():(r=t[u++],l<194||128!=(192&r)?(u--,e()):a.push((31&l)<<6|63&r)):l<240?u>=o-1?e():(r=t[u++],128!=(192&r)||224===l&&r<160||237===l&&r>=160||128!=(192&(c=t[u++]))?(u--,e()):a.push((15&l)<<12|(63&r)<<6|63&c)):l<=244?u>=o-2?e():(r=t[u++],128!=(192&r)||r-144+(l<<28)>>30!=0||128!=(192&(c=t[u++]))||128!=(192&(h=t[u++]))?(u--,e()):(l=(7&l)<<18|(63&r)<<12|(63&c)<<6|63&h,l-=65536,a.push(55296+(l>>10&1023),56320+(1023&l)))):e(),a.length>=8192&&(i=n(i,a),a.length=0)}u=n(i,a)}return u}function Wn(t){const e=Mn(t.g);return Bn(t.g,e)}function zn(t,e,n){var r=Mn(t.g);for(r=t.g.g+r;t.g.g<r;)n.push(e(t.g))}var Kn=[];function Yn(t,e,n){e.g?e.m(t,e.g,e.h,n):e.m(t,e.h,n)}var $n=class{constructor(t,e){this.u=Ne(t,e)}toJSON(){try{var t=Ue(this)}finally{Le=void 0}return t}l(){var t=_o;return t.g?t.l(this,t.g,t.h):t.l(this,t.h,t.defaultValue)}clone(){const t=this.u;return new this.constructor(Ge(t,0|t[Q],!1))}O(){return!!(2&(0|this.u[Q]))}};function qn(t){return t?/^\\d+$/.test(t)?(Vt(t),new Jn(Mt,Pt)):null:Zn||=new Jn(0,0)}$n.prototype.W=ut,$n.prototype.toString=function(){return this.u.toString()};var Jn=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let Zn;function Qn(t){return t?/^-?\\d+$/.test(t)?(Vt(t),new tr(Mt,Pt)):null:er||=new tr(0,0)}var tr=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}};let er;function nr(t,e,n){for(;n>0||e>127;)t.g.push(127&e|128),e=(e>>>7|n<<25)>>>0,n>>>=7;t.g.push(e)}function rr(t,e){for(;e>127;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function ir(t,e){if(e>=0)rr(t,e);else{for(let n=0;n<9;n++)t.g.push(127&e|128),e>>=7;t.g.push(1)}}function sr(t,e){t.g.push(e>>>0&255),t.g.push(e>>>8&255),t.g.push(e>>>16&255),t.g.push(e>>>24&255)}function or(t,e){0!==e.length&&(t.l.push(e),t.h+=e.length)}function ar(t,e,n){rr(t.g,8*e+n)}function cr(t,e){return ar(t,e,2),e=t.g.end(),or(t,e),e.push(t.h),e}function hr(t,e){var n=e.pop();for(n=t.h+t.g.length()-n;n>127;)e.push(127&n|128),n>>>=7,t.h++;e.push(n),t.h++}function ur(t,e,n){ar(t,e,2),rr(t.g,n.length),or(t,t.g.end()),or(t,n)}function lr(t,e,n,r){null!=n&&(e=cr(t,e),r(n,t),hr(t,e))}function dr(){const t=class{constructor(){throw Error()}};return Object.setPrototypeOf(t,t.prototype),t}var fr=dr(),pr=dr(),gr=dr(),mr=dr(),yr=dr(),_r=dr(),vr=dr(),Er=dr(),wr=dr(),Tr=class{constructor(t,e,n){this.g=t,this.h=e,t=fr,this.l=!!t&&n===t||!1}};function Ar(t,e){return new Tr(t,e,fr)}function br(t,e,n,r,i){lr(t,n,Or(e,r),i)}const kr=Ar((function(t,e,n,r,i){return 2===t.h&&(Xn(t,an(e,r,n),i),!0)}),br),Sr=Ar((function(t,e,n,r,i){return 2===t.h&&(Xn(t,an(e,r,n),i),!0)}),br);var xr=Symbol(),Lr=Symbol(),Rr=Symbol(),Fr=Symbol();let Ir,Mr;function Pr(t,e,n,r){var i=r[t];if(i)return i;(i={}).Ma=r,i.T=function(t){switch(typeof t){case\"boolean\":return Re||=[0,void 0,!0];case\"number\":return t>0?void 0:0===t?Fe||=[0,void 0]:[-t,void 0];case\"string\":return[0,t];case\"object\":return t}}(r[0]);var s=r[1];let o=1;s&&s.constructor===Object&&(i.ga=s,\"function\"==typeof(s=r[++o])&&(i.la=!0,Ir??=s,Mr??=r[o+1],s=r[o+=2]));const a={};for(;s&&Array.isArray(s)&&s.length&&\"number\"==typeof s[0]&&s[0]>0;){for(var c=0;c<s.length;c++)a[s[c]]=s;s=r[++o]}for(c=1;void 0!==s;){let t;\"number\"==typeof s&&(c+=s,s=r[++o]);var h=void 0;if(s instanceof Tr?t=s:(t=kr,o--),t?.l){s=r[++o],h=r;var u=o;\"function\"==typeof s&&(s=s(),h[u]=s),h=s}for(u=c+1,\"number\"==typeof(s=r[++o])&&s<0&&(u-=s,s=r[++o]);c<u;c++){const r=a[c];h?n(i,c,t,h,r):e(i,c,t,r)}}return r[t]=i}function Cr(t){return Array.isArray(t)?t[0]instanceof Tr?t:[Sr,t]:[t,void 0]}function Or(t,e){return t instanceof $n?t.u:Array.isArray(t)?De(t,e,!1):void 0}function Ur(t,e,n,r){const i=n.g;t[e]=r?(t,e,n)=>i(t,e,n,r):i}function Dr(t,e,n,r,i){const s=n.g;let o,a;t[e]=(t,e,n)=>s(t,e,n,a||=Pr(Lr,Ur,Dr,r).T,o||=Nr(r),i)}function Nr(t){let e=t[Rr];if(null!=e)return e;const n=Pr(Lr,Ur,Dr,t);return e=n.la?(t,e)=>Ir(t,e,n):(t,e)=>{const r=0|t[Q];for(;jn(e)&&4!=e.h;){var i=e.m,s=n[i];if(null==s){var o=n.ga;o&&(o=o[i])&&(null!=(o=Br(o))&&(s=n[i]=o))}null!=s&&s(e,t,i)||(i=(s=e).l,Vn(s),s.fa?s=void 0:(o=s.g.g-i,s.g.g=i,s=Bn(s.g,o)),i=t,s&&((o=i[q])?o.push(s):i[q]=[s]))}return 8192&r&&it(t),!0},t[Rr]=e}function Br(t){const e=(t=Cr(t))[0].g;if(t=t[1]){const n=Nr(t),r=Pr(Lr,Ur,Dr,t).T;return(t,i,s)=>e(t,i,s,r,n)}return e}function Gr(t,e,n){t[e]=n.h}function jr(t,e,n,r){let i,s;const o=n.h;t[e]=(t,e,n)=>o(t,e,n,s||=Pr(xr,Gr,jr,r).T,i||=Vr(r))}function Vr(t){let e=t[Fr];if(!e){const n=Pr(xr,Gr,jr,t);e=(t,e)=>Xr(t,e,n),t[Fr]=e}return e}function Xr(t,e,n){!function(t,e,n){const r=512&e?0:-1,i=t.length,s=i+((e=64&e?256&e:!!i&&lt(t[i-1]))?-1:0);for(let e=0;e<s;e++)n(e-r,t[e]);if(e){t=t[i-1];for(const e in t)!isNaN(e)&&n(+e,t[e])}}(t,0|t[Q]|(n.T[1]?512:0),((t,r)=>{if(null!=r){var i=function(t,e){var n=t[e];if(n)return n;if((n=t.ga)&&(n=n[e])){var r=(n=Cr(n))[0].h;if(n=n[1]){const e=Vr(n),i=Pr(xr,Gr,jr,n).T;n=t.la?Mr(i,e):(t,n,s)=>r(t,n,s,i,e)}else n=r;return t[e]=n}}(n,t);i&&i(e,r,t)}})),(t=mt(t))&&function(t,e){or(t,t.g.end());for(let n=0;n<e.length;n++)or(t,D(e[n])||new Uint8Array(0))}(e,t)}function Hr(t,e){if(Array.isArray(e)){var n=0|e[Q];if(4&n)return e;for(var r=0,i=0;r<e.length;r++){const n=t(e[r]);null!=n&&(e[i++]=n)}return i<r&&(e.length=i),rt(e,-6145&(5|n)),2&n&&Object.freeze(e),e}}function Wr(t,e,n){return new Tr(t,e,n)}function zr(t,e,n){return new Tr(t,e,n)}function Kr(t,e,n){We(t,0|t[Q],e,n)}var Yr=Ar((function(t,e,n,r,i){return 2===t.h&&(t=Xn(t,De([void 0,void 0],r,!0),i),pt(r=0|e[Q]),(i=Xe(e,r,n))instanceof Ae?0!=(2&i.M)?((i=i.da()).push(t),We(e,r,n,i)):i.Ja(t):Array.isArray(i)?(2&(0|i[Q])&&We(e,r,n,i=Qe(i)),i.push(t)):We(e,r,n,[t]),!0)}),(function(t,e,n,r,i){if(e instanceof Ae)e.forEach(((e,s)=>{lr(t,n,De([s,e],r,!1),i)}));else if(Array.isArray(e))for(let s=0;s<e.length;s++){const o=e[s];Array.isArray(o)&&lr(t,n,De(o,r,!1),i)}}));function $r(t,e,n){if(e=function(t){if(null==t)return t;const e=typeof t;if(\"bigint\"===e)return String(Ht(64,t));if(Qt(t)){if(\"string\"===e)return oe(t);if(\"number\"===e)return se(t)}}(e),null!=e){if(\"string\"==typeof e)Qn(e);if(null!=e)switch(ar(t,n,0),typeof e){case\"number\":t=t.g,Ot(e),nr(t,Mt,Pt);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new tr(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),nr(t.g,n.h,n.g);break;default:n=Qn(e),nr(t.g,n.h,n.g)}}}function qr(t,e,n){null!=(e=te(e))&&null!=e&&(ar(t,n,0),ir(t.g,e))}function Jr(t,e,n){null!=(e=Jt(e))&&(ar(t,n,0),t.g.g.push(e?1:0))}function Zr(t,e,n){null!=(e=fe(e))&&ur(t,n,c(e))}function Qr(t,e,n,r,i){lr(t,n,Or(e,r),i)}function ti(t,e,n){null==e||\"string\"==typeof e||e instanceof N||(C(e)?C(e)&&H(Z):e=void 0),null!=e&&ur(t,n,Ln(e).buffer)}function ei(t,e,n){return(5===t.h||2===t.h)&&(e=en(e,0|e[Q],n,!1),2==t.h?zn(t,Cn,e):e.push(Cn(t.g)),!0)}var ni=Wr((function(t,e,n){if(1!==t.h)return!1;var r=t.g;t=Pn(r);const i=Pn(r);r=2*(i>>31)+1;const s=i>>>20&2047;return t=4294967296*(1048575&i)+t,Kr(e,n,2047==s?t?NaN:r*(1/0):0==s?5e-324*r*t:r*Math.pow(2,s-1075)*(t+4503599627370496)),!0}),(function(t,e,n){null!=(e=qt(e))&&(ar(t,n,1),t=t.g,(n=It||=new DataView(new ArrayBuffer(8))).setFloat64(0,+e,!0),Mt=n.getUint32(0,!0),Pt=n.getUint32(4,!0),sr(t,Mt),sr(t,Pt))}),dr()),ri=Wr((function(t,e,n){return 5===t.h&&(Kr(e,n,Cn(t.g)),!0)}),(function(t,e,n){null!=(e=qt(e))&&(ar(t,n,5),t=t.g,Ut(e),sr(t,Mt))}),vr),ii=zr(ei,(function(t,e,n){if(null!=(e=Hr(qt,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(ar(r,i,5),r=r.g,Ut(s),sr(r,Mt))}}),vr),si=zr(ei,(function(t,e,n){if(null!=(e=Hr(qt,e))&&e.length){ar(t,n,2),rr(t.g,4*e.length);for(let r=0;r<e.length;r++)n=t.g,Ut(e[r]),sr(n,Mt)}}),vr),oi=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,Rn(t.g,Nt)),!0)}),$r,_r),ai=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,0===(t=Rn(t.g,Nt))?void 0:t),!0)}),$r,_r),ci=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,Rn(t.g,Dt)),!0)}),(function(t,e,n){if(null!=(e=ue(e))){if(\"string\"==typeof e)qn(e);if(null!=e)switch(ar(t,n,0),typeof e){case\"number\":t=t.g,Ot(e),nr(t,Mt,Pt);break;case\"bigint\":n=BigInt.asUintN(64,e),n=new Jn(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),nr(t.g,n.h,n.g);break;default:n=qn(e),nr(t.g,n.h,n.g)}}}),dr()),hi=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,In(t.g)),!0)}),qr,mr),ui=zr((function(t,e,n){return(0===t.h||2===t.h)&&(e=en(e,0|e[Q],n,!1),2==t.h?zn(t,In,e):e.push(In(t.g)),!0)}),(function(t,e,n){if(null!=(e=Hr(te,e))&&e.length){n=cr(t,n);for(let n=0;n<e.length;n++)ir(t.g,e[n]);hr(t,n)}}),mr),li=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,0===(t=In(t.g))?void 0:t),!0)}),qr,mr),di=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,Fn(t.g)),!0)}),Jr,pr),fi=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,!1===(t=Fn(t.g))?void 0:t),!0)}),Jr,pr),pi=zr((function(t,e,n){return 2===t.h&&(t=Hn(t),en(e,0|e[Q],n,!1).push(t),!0)}),(function(t,e,n){if(null!=(e=Hr(fe,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&ur(r,i,c(s))}}),gr),gi=Wr((function(t,e,n){return 2===t.h&&(Kr(e,n,\"\"===(t=Hn(t))?void 0:t),!0)}),Zr,gr),mi=Wr((function(t,e,n){return 2===t.h&&(Kr(e,n,Hn(t)),!0)}),Zr,gr),yi=function(t,e,n=fr){return new Tr(t,e,n)}((function(t,e,n,r,i){return 2===t.h&&(r=De(void 0,r,!0),en(e,0|e[Q],n,!0).push(r),Xn(t,r,i),!0)}),(function(t,e,n,r,i){if(Array.isArray(e))for(let s=0;s<e.length;s++)Qr(t,e[s],n,r,i)})),_i=Ar((function(t,e,n,r,i,s){return 2===t.h&&(sn(e,0|e[Q],s,n),Xn(t,e=an(e,r,n),i),!0)}),Qr),vi=Wr((function(t,e,n){return 2===t.h&&(Kr(e,n,Wn(t)),!0)}),ti,Er),Ei=zr((function(t,e,n){return(0===t.h||2===t.h)&&(e=en(e,0|e[Q],n,!1),2==t.h?zn(t,Mn,e):e.push(Mn(t.g)),!0)}),(function(t,e,n){if(null!=(e=Hr(ee,e)))for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];null!=s&&(ar(r,i,0),rr(r.g,s))}}),yr),wi=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,0===(t=Mn(t.g))?void 0:t),!0)}),(function(t,e,n){null!=(e=ee(e))&&null!=e&&(ar(t,n,0),rr(t.g,e))}),yr),Ti=Wr((function(t,e,n){return 0===t.h&&(Kr(e,n,In(t.g)),!0)}),(function(t,e,n){null!=(e=te(e))&&(e=parseInt(e,10),ar(t,n,0),ir(t.g,e))}),wr);class Ai{constructor(t,e){this.h=t,this.g=e,this.l=hn,this.m=dn,this.defaultValue=void 0}register(){w(this)}}function bi(t,e){return new Ai(t,e)}function ki(t,e){return(n,r)=>{if(Kn.length){const t=Kn.pop();t.o(r),Un(t.g,n,r),n=t}else n=new class{constructor(t,e){if(Gn.length){const n=Gn.pop();Un(n,t,e),t=n}else t=new class{constructor(t,e){this.h=null,this.m=!1,this.g=this.l=this.j=0,Un(this,t,e)}clear(){this.h=null,this.m=!1,this.g=this.l=this.j=0,this.aa=!1}}(t,e);this.g=t,this.l=this.g.g,this.h=this.m=-1,this.o(e)}o({fa:t=!1}={}){this.fa=t}}(n,r);try{const r=new t,s=r.u;Nr(e)(s,n);var i=r}finally{n.g.clear(),n.m=-1,n.h=-1,Kn.length<100&&Kn.push(n)}return i}}function Si(t){return function(){const e=new class{constructor(){this.l=[],this.h=0,this.g=new class{constructor(){this.g=[]}length(){return this.g.length}end(){const t=this.g;return this.g=[],t}}}};Xr(this.u,e,Pr(xr,Gr,jr,t)),or(e,e.g.end());const n=new Uint8Array(e.h),r=e.l,i=r.length;let s=0;for(let t=0;t<i;t++){const e=r[t];n.set(e,s),s+=e.length}return e.l=[n],n}}var xi=class extends $n{constructor(t){super(t)}},Li=[0,gi,Wr((function(t,e,n){return 2===t.h&&(Kr(e,n,(t=Wn(t))===U()?void 0:t),!0)}),(function(t,e,n){if(null!=e){if(e instanceof $n){const r=e.Oa;return void(r&&(e=r(e),null!=e&&ur(t,n,Ln(e).buffer)))}if(Array.isArray(e))return}ti(t,e,n)}),Er)];let Ri,Fi=globalThis.trustedTypes;function Ii(t){void 0===Ri&&(Ri=function(){let t=null;if(!Fi)return t;try{const e=t=>t;t=Fi.createPolicy(\"goog#html\",{createHTML:e,createScript:e,createScriptURL:e})}catch(t){}return t}());var e=Ri;return new class{constructor(t){this.g=t}toString(){return this.g+\"\"}}(e?e.createScriptURL(t):t)}function Mi(t,...e){if(0===e.length)return Ii(t[0]);let n=t[0];for(let r=0;r<e.length;r++)n+=encodeURIComponent(e[r])+t[r+1];return Ii(n)}var Pi=[0,hi,Ti,di,-1,ui,Ti,-1],Ci=class extends $n{constructor(t){super(t)}},Oi=[0,di,mi,di,Ti,-1,zr((function(t,e,n){return(0===t.h||2===t.h)&&(e=en(e,0|e[Q],n,!1),2==t.h?zn(t,On,e):e.push(In(t.g)),!0)}),(function(t,e,n){if(null!=(e=Hr(te,e))&&e.length){n=cr(t,n);for(let n=0;n<e.length;n++)ir(t.g,e[n]);hr(t,n)}}),wr),mi,-1,[0,di,-1],Ti,di,-1],Ui=[0,mi,-2],Di=class extends $n{constructor(t){super(t)}},Ni=[0],Bi=[0,hi,di,1,di,-3],Gi=class extends $n{constructor(t){super(t,2)}},ji={};ji[336783863]=[0,mi,di,-1,hi,[0,[1,2,3,4,5,6,7,8,9],_i,Ni,_i,Oi,_i,Ui,_i,Bi,_i,Pi,_i,[0,mi,-2],_i,[0,mi,Ti],_i,[0,Ti,mi,-1],_i,[0,Ti,-1]],[0,mi],di,[0,[1,3],[2,4],_i,[0,ui],-1,_i,[0,pi],-1,yi,[0,mi,-1]],mi];var Vi=[0,ai,-1,fi,-3,ai,ui,gi,li,ai,-1,fi,li,fi,-2,gi];function Xi(t,e){tn(t,2,de(e),\"\")}function Hi(t,e){mn(t,3,e)}function Wi(t,e){mn(t,4,e)}var zi=class extends $n{constructor(t){super(t,500)}o(t){return dn(this,0,7,t)}},Ki=[-1,{}],Yi=[0,mi,1,Ki],$i=[0,mi,pi,Ki];function qi(t,e){yn(t,1,zi,e)}function Ji(t,e){mn(t,10,e)}function Zi(t,e){mn(t,15,e)}var Qi=class extends $n{constructor(t){super(t,500)}o(t){return dn(this,0,1001,t)}},ts=[-500,yi,[-500,gi,-1,pi,-3,[-2,ji,di],yi,Li,li,-1,Yi,$i,yi,[0,gi,fi],gi,Vi,li,pi,987,pi],4,yi,[-500,mi,-1,[-1,{}],998,mi],yi,[-500,mi,pi,-1,[-2,{},di],997,pi,-1],li,yi,[-500,mi,pi,Ki,998,pi],pi,li,Yi,$i,yi,[0,gi,-1,Ki],pi,-2,Vi,gi,-1,fi,[0,fi,wi],978,Ki,yi,Li];Qi.prototype.g=Si(ts);var es=ki(Qi,ts),ns=class extends $n{constructor(t){super(t)}},rs=class extends $n{constructor(t){super(t)}g(){return ln(this,ns,1)}},is=[0,yi,[0,hi,ri,mi,-1]],ss=ki(rs,is),os=class extends $n{constructor(t){super(t)}},as=class extends $n{constructor(t){super(t)}},cs=class extends $n{constructor(t){super(t)}h(){return hn(this,os,2)}g(){return ln(this,as,5)}},hs=ki(class extends $n{constructor(t){super(t)}},[0,pi,ui,si,[0,Ti,[0,hi,-3],[0,ri,-3],[0,hi,-1,[0,yi,[0,hi,-2]]],yi,[0,ri,-1,mi,ri]],mi,-1,oi,yi,[0,hi,ri],pi,oi]),us=class extends $n{constructor(t){super(t)}},ls=ki(class extends $n{constructor(t){super(t)}},[0,yi,[0,ri,-4]]),ds=class extends $n{constructor(t){super(t)}},fs=ki(class extends $n{constructor(t){super(t)}},[0,yi,[0,ri,-4]]),ps=class extends $n{constructor(t){super(t)}},gs=[0,hi,-1,si,Ti],ms=class extends $n{constructor(t){super(t)}};ms.prototype.g=Si([0,ri,-4,oi]);var ys=class extends $n{constructor(t){super(t)}},_s=ki(class extends $n{constructor(t){super(t)}},[0,yi,[0,1,hi,mi,is],oi]),vs=class extends $n{constructor(t){super(t)}},Es=class extends $n{constructor(t){super(t)}ma(){const t=Ke(this);return null==t?U():t}},ws=class extends $n{constructor(t){super(t)}},Ts=[1,2],As=ki(class extends $n{constructor(t){super(t)}},[0,yi,[0,Ts,_i,[0,si],_i,[0,vi],hi,mi],oi]),bs=class extends $n{constructor(t){super(t)}},ks=[0,mi,hi,ri,pi,-1],Ss=class extends $n{constructor(t){super(t)}},xs=[0,di,-1],Ls=class extends $n{constructor(t){super(t)}},Rs=[1,2,3,4,5],Fs=class extends $n{constructor(t){super(t)}g(){return null!=Ke(this)}h(){return null!=vn(this,2)}},Is=class extends $n{constructor(t){super(t)}g(){return Jt(Ve(this,2))??!1}},Ms=[0,vi,mi,[0,hi,oi,-1],[0,ci,oi]],Ps=[0,Ms,di,[0,Rs,_i,Bi,_i,Oi,_i,Pi,_i,Ni,_i,Ui],Ti],Cs=class extends $n{constructor(t){super(t)}},Os=[0,Ps,ri,-1,hi],Us=bi(502141897,Cs);ji[502141897]=Os;var Ds=ki(class extends $n{constructor(t){super(t)}},[0,[0,Ti,-1,ii,Ei],gs]),Ns=class extends $n{constructor(t){super(t)}},Bs=class extends $n{constructor(t){super(t)}},Gs=[0,Ps,ri,[0,Ps],di],js=[0,Ps,Os,Gs,ri,[0,[0,Ms]]],Vs=bi(508968150,Bs);ji[508968150]=js,ji[508968149]=Gs;var Xs=class extends $n{constructor(t){super(t)}},Hs=bi(513916220,Xs);ji[513916220]=[0,Ps,js,hi];var Ws=class extends $n{constructor(t){super(t)}h(){return hn(this,bs,2)}g(){He(this,2)}},zs=[0,Ps,ks];ji[478825465]=zs;var Ks=class extends $n{constructor(t){super(t)}},Ys=class extends $n{constructor(t){super(t)}},$s=class extends $n{constructor(t){super(t)}},qs=class extends $n{constructor(t){super(t)}},Js=class extends $n{constructor(t){super(t)}},Zs=[0,Ps,[0,Ps],zs,-1],Qs=[0,Ps,ri,hi],to=[0,Ps,ri],eo=[0,Ps,Qs,to,ri],no=bi(479097054,Js);ji[479097054]=[0,Ps,eo,Zs],ji[463370452]=Zs,ji[464864288]=Qs;var ro=bi(462713202,qs);ji[462713202]=eo,ji[474472470]=to;var io=class extends $n{constructor(t){super(t)}},so=class extends $n{constructor(t){super(t)}},oo=class extends $n{constructor(t){super(t)}},ao=class extends $n{constructor(t){super(t)}},co=[0,Ps,ri,-1,hi],ho=[0,Ps,ri,di];ao.prototype.g=Si([0,Ps,to,[0,Ps],Os,Gs,co,ho]);var uo=class extends $n{constructor(t){super(t)}},lo=bi(456383383,uo);ji[456383383]=[0,Ps,ks];var fo=class extends $n{constructor(t){super(t)}},po=bi(476348187,fo);ji[476348187]=[0,Ps,xs];var go=class extends $n{constructor(t){super(t)}},mo=class extends $n{constructor(t){super(t)}},yo=[0,Ti,-1],_o=bi(458105876,class extends $n{constructor(t){super(t)}g(){var t=this.u;const e=0|t[Q],n=2&e;return t=function(t,e,n){var r=mo;const i=2&e;let s=!1;if(null==n){if(i)return Ie();n=[]}else if(n.constructor===Ae){if(0==(2&n.M)||i)return n;n=n.da()}else Array.isArray(n)?s=!!(2&(0|n[Q])):n=[];if(i){if(!n.length)return Ie();s||(s=!0,it(n))}else s&&(s=!1,n=Qe(n));return s||(64&(0|n[Q])?n[Q]&=-33:32&e&&nt(n,32)),We(t,e,2,r=new Ae(n,r,ge,void 0)),r}(t,e,Xe(t,e,2)),!n&&mo&&(t.pa=!0),t}});ji[458105876]=[0,yo,Yr,[!0,oi,[0,mi,-1,pi]]];var vo=class extends $n{constructor(t){super(t)}},Eo=bi(458105758,vo);ji[458105758]=[0,Ps,mi,yo];var wo=class extends $n{constructor(t){super(t)}},To=bi(443442058,wo);ji[443442058]=[0,Ps,mi,hi,ri,pi,-1,di,ri],ji[514774813]=co;var Ao=class extends $n{constructor(t){super(t)}},bo=bi(516587230,Ao);function ko(t,e){return e=e?e.clone():new bs,void 0!==t.displayNamesLocale?He(e,1,de(t.displayNamesLocale)):void 0===t.displayNamesLocale&&He(e,1),void 0!==t.maxResults?Tn(e,2,t.maxResults):\"maxResults\"in t&&He(e,2),void 0!==t.scoreThreshold?An(e,3,t.scoreThreshold):\"scoreThreshold\"in t&&He(e,3),void 0!==t.categoryAllowlist?bn(e,4,t.categoryAllowlist):\"categoryAllowlist\"in t&&He(e,4),void 0!==t.categoryDenylist?bn(e,5,t.categoryDenylist):\"categoryDenylist\"in t&&He(e,5),e}function So(t,e=-1,n=\"\"){return{categories:t.map((t=>({index:_n(t,1)??0??-1,score:En(t,2)??0,categoryName:vn(t,3)??\"\"??\"\",displayName:vn(t,4)??\"\"??\"\"}))),headIndex:e,headName:n}}function xo(t){var e=$e(t,3,qt,Ye()),n=$e(t,2,te,Ye()),r=$e(t,1,fe,Ye()),i=$e(t,9,fe,Ye());const s={categories:[],keypoints:[]};for(let t=0;t<e.length;t++)s.categories.push({score:e[t],index:n[t]??-1,categoryName:r[t]??\"\",displayName:i[t]??\"\"});if((e=hn(t,cs,4)?.h())&&(s.boundingBox={originX:_n(e,1)??0,originY:_n(e,2)??0,width:_n(e,3)??0,height:_n(e,4)??0,angle:0}),hn(t,cs,4)?.g().length)for(const e of hn(t,cs,4).g())s.keypoints.push({x:ze(e,1)??0,y:ze(e,2)??0,score:ze(e,4)??0,label:vn(e,3)??\"\"});return s}function Lo(t){const e=[];for(const n of ln(t,ds,1))e.push({x:En(n,1)??0,y:En(n,2)??0,z:En(n,3)??0,visibility:En(n,4)??0});return e}function Ro(t){const e=[];for(const n of ln(t,us,1))e.push({x:En(n,1)??0,y:En(n,2)??0,z:En(n,3)??0,visibility:En(n,4)??0});return e}function Fo(t){return Array.from(t,(t=>t>127?t-256:t))}function Io(t,e){if(t.length!==e.length)throw Error(`Cannot compute cosine similarity between embeddings of different sizes (${t.length} vs. ${e.length}).`);let n=0,r=0,i=0;for(let s=0;s<t.length;s++)n+=t[s]*e[s],r+=t[s]*t[s],i+=e[s]*e[s];if(r<=0||i<=0)throw Error(\"Cannot compute cosine similarity on embedding with 0 norm.\");return n/Math.sqrt(r*i)}let Mo;ji[516587230]=[0,Ps,co,ho,ri],ji[518928384]=ho;const Po=new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11]);async function Co(){if(void 0===Mo)try{await WebAssembly.instantiate(Po),Mo=!0}catch{Mo=!1}return Mo}async function Oo(t,e=Mi``){const n=await Co()?\"wasm_internal\":\"wasm_nosimd_internal\";return{wasmLoaderPath:`${e}/${t}_${n}.js`,wasmBinaryPath:`${e}/${t}_${n}.wasm`}}var Uo=class{};function Do(){var t=navigator;return\"undefined\"!=typeof OffscreenCanvas&&(!function(t=navigator){return(t=t.userAgent).includes(\"Safari\")&&!t.includes(\"Chrome\")}(t)||!!((t=t.userAgent.match(/Version\\/([\\d]+).*Safari/))&&t.length>=1&&Number(t[1])>=17))}async function No(t){if(\"function\"!=typeof importScripts){const e=document.createElement(\"script\");return e.src=t.toString(),e.crossOrigin=\"anonymous\",new Promise(((t,n)=>{e.addEventListener(\"load\",(()=>{t()}),!1),e.addEventListener(\"error\",(t=>{n(t)}),!1),document.body.appendChild(e)}))}importScripts(t.toString())}function Bo(t){return void 0!==t.videoWidth?[t.videoWidth,t.videoHeight]:void 0!==t.naturalWidth?[t.naturalWidth,t.naturalHeight]:void 0!==t.displayWidth?[t.displayWidth,t.displayHeight]:[t.width,t.height]}function Go(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\"),n(e=t.i.stringToNewUTF8(e)),t.i._free(e)}function jo(t,e,n){if(!t.i.canvas)throw Error(\"No OpenGL canvas configured.\");if(n?t.i._bindTextureToStream(n):t.i._bindTextureToCanvas(),!(n=t.i.canvas.getContext(\"webgl2\")||t.i.canvas.getContext(\"webgl\")))throw Error(\"Failed to obtain WebGL context from the provided canvas. `getContext()` should only be invoked with `webgl` or `webgl2`.\");t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!0),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e),t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!1);const[r,i]=Bo(e);return!t.l||r===t.i.canvas.width&&i===t.i.canvas.height||(t.i.canvas.width=r,t.i.canvas.height=i),[r,i]}function Vo(t,e,n){t.m||console.error(\"No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target\");const r=new Uint32Array(e.length);for(let n=0;n<e.length;n++)r[n]=t.i.stringToNewUTF8(e[n]);e=t.i._malloc(4*r.length),t.i.HEAPU32.set(r,e>>2),n(e);for(const e of r)t.i._free(e);t.i._free(e)}function Xo(t,e,n){t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=n}function Ho(t,e,n){let r=[];t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=(t,e,i)=>{e?(n(r,i),r=[]):r.push(t)}}Uo.forVisionTasks=function(t){return Oo(\"vision\",t)},Uo.forTextTasks=function(t){return Oo(\"text\",t)},Uo.forGenAiExperimentalTasks=function(t){return Oo(\"genai_experimental\",t)},Uo.forGenAiTasks=function(t){return Oo(\"genai\",t)},Uo.forAudioTasks=function(t){return Oo(\"audio\",t)},Uo.isSimdSupported=function(){return Co()};async function Wo(t,e,n,r){return t=await(async(t,e,n,r,i)=>{if(e&&await No(e),!self.ModuleFactory)throw Error(\"ModuleFactory not set.\");if(n&&(await No(n),!self.ModuleFactory))throw Error(\"ModuleFactory not set.\");return self.Module&&i&&((e=self.Module).locateFile=i.locateFile,i.mainScriptUrlOrBlob&&(e.mainScriptUrlOrBlob=i.mainScriptUrlOrBlob)),i=await self.ModuleFactory(self.Module||i),self.ModuleFactory=self.Module=void 0,new t(i,r)})(t,n.wasmLoaderPath,n.assetLoaderPath,e,{locateFile:t=>t.endsWith(\".wasm\")?n.wasmBinaryPath.toString():n.assetBinaryPath&&t.endsWith(\".data\")?n.assetBinaryPath.toString():t}),await t.o(r),t}function zo(t,e){const n=hn(t.baseOptions,Fs,1)||new Fs;\"string\"==typeof e?(He(n,2,de(e)),He(n,1)):e instanceof Uint8Array&&(He(n,1,dt(e,!1)),He(n,2)),dn(t.baseOptions,0,1,n)}function Ko(t){try{const e=t.G.length;if(1===e)throw Error(t.G[0].message);if(e>1)throw Error(\"Encountered multiple errors: \"+t.G.map((t=>t.message)).join(\", \"))}finally{t.G=[]}}function Yo(t,e){t.B=Math.max(t.B,e)}function $o(t,e){t.A=new zi,Xi(t.A,\"PassThroughCalculator\"),Hi(t.A,\"free_memory\"),Wi(t.A,\"free_memory_unused_out\"),Ji(e,\"free_memory\"),qi(e,t.A)}function qo(t,e){Hi(t.A,e),Wi(t.A,e+\"_unused_out\")}function Jo(t){t.g.addBoolToStream(!0,\"free_memory\",t.B)}var Zo=class{constructor(t){this.g=t,this.G=[],this.B=0,this.g.setAutoRenderToScreen(!1)}l(t,e=!0){if(e){const e=t.baseOptions||{};if(t.baseOptions?.modelAssetBuffer&&t.baseOptions?.modelAssetPath)throw Error(\"Cannot set both baseOptions.modelAssetPath and baseOptions.modelAssetBuffer\");if(!(hn(this.baseOptions,Fs,1)?.g()||hn(this.baseOptions,Fs,1)?.h()||t.baseOptions?.modelAssetBuffer||t.baseOptions?.modelAssetPath))throw Error(\"Either baseOptions.modelAssetPath or baseOptions.modelAssetBuffer must be set\");if(function(t,e){let n=hn(t.baseOptions,Ls,3);if(!n){var r=n=new Ls,i=new Di;fn(r,4,Rs,i)}\"delegate\"in e&&(\"GPU\"===e.delegate?(e=n,r=new Ci,fn(e,2,Rs,r)):(e=n,r=new Di,fn(e,4,Rs,r))),dn(t.baseOptions,0,3,n)}(this,e),e.modelAssetPath)return fetch(e.modelAssetPath.toString()).then((t=>{if(t.ok)return t.arrayBuffer();throw Error(`Failed to fetch model: ${e.modelAssetPath} (${t.status})`)})).then((t=>{try{this.g.i.FS_unlink(\"/model.dat\")}catch{}this.g.i.FS_createDataFile(\"/\",\"model.dat\",new Uint8Array(t),!0,!1,!1),zo(this,\"/model.dat\"),this.m(),this.J()}));if(e.modelAssetBuffer instanceof Uint8Array)zo(this,e.modelAssetBuffer);else if(e.modelAssetBuffer)return async function(t){const e=[];for(var n=0;;){const{done:r,value:i}=await t.read();if(r)break;e.push(i),n+=i.length}if(0===e.length)return new Uint8Array(0);if(1===e.length)return e[0];t=new Uint8Array(n),n=0;for(const r of e)t.set(r,n),n+=r.length;return t}(e.modelAssetBuffer).then((t=>{zo(this,t),this.m(),this.J()}))}return this.m(),this.J(),Promise.resolve()}J(){}ca(){let t;if(this.g.ca((e=>{t=es(e)})),!t)throw Error(\"Failed to retrieve CalculatorGraphConfig\");return t}setGraph(t,e){this.g.attachErrorListener(((t,e)=>{this.G.push(Error(e))})),this.g.Ha(),this.g.setGraph(t,e),this.A=void 0,Ko(this)}finishProcessing(){this.g.finishProcessing(),Ko(this)}close(){this.A=void 0,this.g.closeGraph()}};function Qo(t,e){if(!t)throw Error(`Unable to obtain required WebGL resource: ${e}`);return t}Zo.prototype.close=Zo.prototype.close;class ta{constructor(t,e,n,r){this.g=t,this.h=e,this.m=n,this.l=r}bind(){this.g.bindVertexArray(this.h)}close(){this.g.deleteVertexArray(this.h),this.g.deleteBuffer(this.m),this.g.deleteBuffer(this.l)}}function ea(t,e,n){const r=t.g;if(n=Qo(r.createShader(n),\"Failed to create WebGL shader\"),r.shaderSource(n,e),r.compileShader(n),!r.getShaderParameter(n,r.COMPILE_STATUS))throw Error(`Could not compile WebGL shader: ${r.getShaderInfoLog(n)}`);return r.attachShader(t.h,n),n}function na(t,e){const n=t.g,r=Qo(n.createVertexArray(),\"Failed to create vertex array\");n.bindVertexArray(r);const i=Qo(n.createBuffer(),\"Failed to create buffer\");n.bindBuffer(n.ARRAY_BUFFER,i),n.enableVertexAttribArray(t.P),n.vertexAttribPointer(t.P,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW);const s=Qo(n.createBuffer(),\"Failed to create buffer\");return n.bindBuffer(n.ARRAY_BUFFER,s),n.enableVertexAttribArray(t.J),n.vertexAttribPointer(t.J,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array(e?[0,1,0,0,1,0,1,1]:[0,0,0,1,1,1,1,0]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.bindVertexArray(null),new ta(n,r,i,s)}function ra(t,e){if(t.g){if(e!==t.g)throw Error(\"Cannot change GL context once initialized\")}else t.g=e}function ia(t,e,n,r){return ra(t,e),t.h||(t.m(),t.C()),n?(t.s||(t.s=na(t,!0)),n=t.s):(t.v||(t.v=na(t,!1)),n=t.v),e.useProgram(t.h),n.bind(),t.l(),t=r(),n.g.bindVertexArray(null),t}function sa(t,e,n){return ra(t,e),t=Qo(e.createTexture(),\"Failed to create texture\"),e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,n??e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,n??e.LINEAR),e.bindTexture(e.TEXTURE_2D,null),t}function oa(t,e,n){ra(t,e),t.A||(t.A=Qo(e.createFramebuffer(),\"Failed to create framebuffe.\")),e.bindFramebuffer(e.FRAMEBUFFER,t.A),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0)}function aa(t){t.g?.bindFramebuffer(t.g.FRAMEBUFFER,null)}var ca=class{G(){return\"\\n  precision mediump float;\\n  varying vec2 vTex;\\n  uniform sampler2D inputTexture;\\n  void main() {\\n    gl_FragColor = texture2D(inputTexture, vTex);\\n  }\\n \"}m(){const t=this.g;if(this.h=Qo(t.createProgram(),\"Failed to create WebGL program\"),this.Z=ea(this,\"\\n  attribute vec2 aVertex;\\n  attribute vec2 aTex;\\n  varying vec2 vTex;\\n  void main(void) {\\n    gl_Position = vec4(aVertex, 0.0, 1.0);\\n    vTex = aTex;\\n  }\",t.VERTEX_SHADER),this.Y=ea(this,this.G(),t.FRAGMENT_SHADER),t.linkProgram(this.h),!t.getProgramParameter(this.h,t.LINK_STATUS))throw Error(`Error during program linking: ${t.getProgramInfoLog(this.h)}`);this.P=t.getAttribLocation(this.h,\"aVertex\"),this.J=t.getAttribLocation(this.h,\"aTex\")}C(){}l(){}close(){if(this.h){const t=this.g;t.deleteProgram(this.h),t.deleteShader(this.Z),t.deleteShader(this.Y)}this.A&&this.g.deleteFramebuffer(this.A),this.v&&this.v.close(),this.s&&this.s.close()}};var ha=class extends ca{G(){return\"\\n  precision mediump float;\\n  uniform sampler2D backgroundTexture;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D colorMappingTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    vec4 backgroundColor = texture2D(backgroundTexture, vTex);\\n    float category = texture2D(maskTexture, vTex).r;\\n    vec4 categoryColor = texture2D(colorMappingTexture, vec2(category, 0.0));\\n    gl_FragColor = mix(backgroundColor, categoryColor, categoryColor.a);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.B=sa(this,t,t.LINEAR),t.activeTexture(t.TEXTURE2),this.j=sa(this,t,t.NEAREST)}m(){super.m();const t=this.g;this.L=Qo(t.getUniformLocation(this.h,\"backgroundTexture\"),\"Uniform location\"),this.U=Qo(t.getUniformLocation(this.h,\"colorMappingTexture\"),\"Uniform location\"),this.K=Qo(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.K,0),t.uniform1i(this.L,1),t.uniform1i(this.U,2)}close(){this.B&&this.g.deleteTexture(this.B),this.j&&this.g.deleteTexture(this.j),super.close()}},ua=class extends ca{G(){return\"\\n  precision mediump float;\\n  uniform sampler2D maskTexture;\\n  uniform sampler2D defaultTexture;\\n  uniform sampler2D overlayTexture;\\n  varying vec2 vTex;\\n  void main() {\\n    float confidence = texture2D(maskTexture, vTex).r;\\n    vec4 defaultColor = texture2D(defaultTexture, vTex);\\n    vec4 overlayColor = texture2D(overlayTexture, vTex);\\n    // Apply the alpha from the overlay and merge in the default color\\n    overlayColor = mix(defaultColor, overlayColor, overlayColor.a);\\n    gl_FragColor = mix(defaultColor, overlayColor, confidence);\\n  }\\n \"}C(){const t=this.g;t.activeTexture(t.TEXTURE1),this.j=sa(this,t),t.activeTexture(t.TEXTURE2),this.B=sa(this,t)}m(){super.m();const t=this.g;this.K=Qo(t.getUniformLocation(this.h,\"defaultTexture\"),\"Uniform location\"),this.L=Qo(t.getUniformLocation(this.h,\"overlayTexture\"),\"Uniform location\"),this.H=Qo(t.getUniformLocation(this.h,\"maskTexture\"),\"Uniform location\")}l(){super.l();const t=this.g;t.uniform1i(this.H,0),t.uniform1i(this.K,1),t.uniform1i(this.L,2)}close(){this.j&&this.g.deleteTexture(this.j),this.B&&this.g.deleteTexture(this.B),super.close()}};function la(t,e){switch(e){case 0:return t.g.find((t=>t instanceof Uint8Array));case 1:return t.g.find((t=>t instanceof Float32Array));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function da(t){var e=la(t,1);if(!e){if(e=la(t,0))e=new Float32Array(e).map((t=>t/255));else{e=new Float32Array(t.width*t.height);const r=pa(t);var n=ma(t);if(oa(n,r,fa(t)),\"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod\".split(\";\").includes(navigator.platform)||navigator.userAgent.includes(\"Mac\")&&\"document\"in self&&\"ontouchend\"in self.document){n=new Float32Array(t.width*t.height*4),r.readPixels(0,0,t.width,t.height,r.RGBA,r.FLOAT,n);for(let t=0,r=0;t<e.length;++t,r+=4)e[t]=n[r]}else r.readPixels(0,0,t.width,t.height,r.RED,r.FLOAT,e)}t.g.push(e)}return e}function fa(t){let e=la(t,2);if(!e){const n=pa(t);e=ya(t);const r=da(t),i=ga(t);n.texImage2D(n.TEXTURE_2D,0,i,t.width,t.height,0,n.RED,n.FLOAT,r),_a(t)}return e}function pa(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when initializing the image.\");return t.h||(t.h=Qo(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function ga(t){if(t=pa(t),!va)if(t.getExtension(\"EXT_color_buffer_float\")&&t.getExtension(\"OES_texture_float_linear\")&&t.getExtension(\"EXT_float_blend\"))va=t.R32F;else{if(!t.getExtension(\"EXT_color_buffer_half_float\"))throw Error(\"GPU does not fully support 4-channel float32 or float16 formats\");va=t.R16F}return va}function ma(t){return t.l||(t.l=new ca),t.l}function ya(t){const e=pa(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=la(t,2);return n||(n=sa(ma(t),e,t.m?e.LINEAR:e.NEAREST),t.g.push(n),t.j=!0),e.bindTexture(e.TEXTURE_2D,n),n}function _a(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}var va,Ea=class{constructor(t,e,n,r,i,s,o){this.g=t,this.m=e,this.j=n,this.canvas=r,this.l=i,this.width=s,this.height=o,this.j&&(0===--wa&&console.error(\"You seem to be creating MPMask instances without invoking .close(). This leaks resources.\"))}Da(){return!!la(this,0)}ja(){return!!la(this,1)}R(){return!!la(this,2)}ia(){return(e=la(t=this,0))||(e=da(t),e=new Uint8Array(e.map((t=>255*t))),t.g.push(e)),e;var t,e}ha(){return da(this)}N(){return fa(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof Uint8Array)n=new Uint8Array(e);else if(e instanceof Float32Array)n=new Float32Array(e);else{if(!(e instanceof WebGLTexture))throw Error(`Type is not supported: ${e}`);{const t=pa(this),e=ma(this);t.activeTexture(t.TEXTURE1),n=sa(e,t,this.m?t.LINEAR:t.NEAREST),t.bindTexture(t.TEXTURE_2D,n);const r=ga(this);t.texImage2D(t.TEXTURE_2D,0,r,this.width,this.height,0,t.RED,t.FLOAT,null),t.bindTexture(t.TEXTURE_2D,null),oa(e,t,n),ia(e,t,!1,(()=>{ya(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),_a(this)})),aa(e),_a(this)}}t.push(n)}return new Ea(t,this.m,this.R(),this.canvas,this.l,this.width,this.height)}close(){this.j&&pa(this).deleteTexture(la(this,2)),wa=-1}};Ea.prototype.close=Ea.prototype.close,Ea.prototype.clone=Ea.prototype.clone,Ea.prototype.getAsWebGLTexture=Ea.prototype.N,Ea.prototype.getAsFloat32Array=Ea.prototype.ha,Ea.prototype.getAsUint8Array=Ea.prototype.ia,Ea.prototype.hasWebGLTexture=Ea.prototype.R,Ea.prototype.hasFloat32Array=Ea.prototype.ja,Ea.prototype.hasUint8Array=Ea.prototype.Da;var wa=250;const Ta={color:\"white\",lineWidth:4,radius:6};function Aa(t){return{...Ta,fillColor:(t=t||{}).color,...t}}function ba(t,e){return t instanceof Function?t(e):t}function ka(t,e,n){return Math.max(Math.min(e,n),Math.min(Math.max(e,n),t))}function Sa(t){if(!t.l)throw Error(\"CPU rendering requested but CanvasRenderingContext2D not provided.\");return t.l}function xa(t){if(!t.j)throw Error(\"GPU rendering requested but WebGL2RenderingContext not provided.\");return t.j}function La(t,e,n){if(e.R())n(e.N());else{const r=e.ja()?e.ha():e.ia();t.m=t.m??new ca;const i=xa(t);n((t=new Ea([r],e.m,!1,i.canvas,t.m,e.width,e.height)).N()),t.close()}}function Ra(t,e,n,r){const i=function(t){return t.g||(t.g=new ha),t.g}(t),s=xa(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n;ia(i,s,!0,(()=>{!function(t,e,n,r){const i=t.g;if(i.activeTexture(i.TEXTURE0),i.bindTexture(i.TEXTURE_2D,e),i.activeTexture(i.TEXTURE1),i.bindTexture(i.TEXTURE_2D,t.B),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,i.RGBA,i.UNSIGNED_BYTE,n),t.H&&function(t,e){if(t!==e)return!1;t=t.entries(),e=e.entries();for(const[r,i]of t){t=r;const s=i;var n=e.next();if(n.done)return!1;const[o,a]=n.value;if(n=a,t!==o||s[0]!==n[0]||s[1]!==n[1]||s[2]!==n[2]||s[3]!==n[3])return!1}return!!e.next().done}(t.H,r))i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j);else{t.H=r;const e=Array(1024).fill(0);r.forEach(((t,n)=>{if(4!==t.length)throw Error(`Color at index ${n} is not a four-channel value.`);e[4*n]=t[0],e[4*n+1]=t[1],e[4*n+2]=t[2],e[4*n+3]=t[3]})),i.activeTexture(i.TEXTURE2),i.bindTexture(i.TEXTURE_2D,t.j),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,256,1,0,i.RGBA,i.UNSIGNED_BYTE,new Uint8Array(e))}}(i,e,o,r),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.drawArrays(s.TRIANGLE_FAN,0,4);const t=i.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}function Fa(t,e,n,r){const i=xa(t),s=function(t){return t.h||(t.h=new ua),t.h}(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n,a=Array.isArray(r)?new ImageData(new Uint8ClampedArray(r),1,1):r;ia(s,i,!0,(()=>{var t=s.g;t.activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,s.j),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,o),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,s.B),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,a),i.clearColor(0,0,0,0),i.clear(i.COLOR_BUFFER_BIT),i.drawArrays(i.TRIANGLE_FAN,0,4),i.bindTexture(i.TEXTURE_2D,null),(t=s.g).activeTexture(t.TEXTURE0),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,null)}))}var Ia=class{constructor(t,e){\"undefined\"!=typeof CanvasRenderingContext2D&&t instanceof CanvasRenderingContext2D||t instanceof OffscreenCanvasRenderingContext2D?(this.l=t,this.j=e):this.j=t}wa(t,e){if(t){var n=Sa(this);e=Aa(e),n.save();var r=n.canvas,i=0;for(const s of t)n.fillStyle=ba(e.fillColor,{index:i,from:s}),n.strokeStyle=ba(e.color,{index:i,from:s}),n.lineWidth=ba(e.lineWidth,{index:i,from:s}),(t=new Path2D).arc(s.x*r.width,s.y*r.height,ba(e.radius,{index:i,from:s}),0,2*Math.PI),n.fill(t),n.stroke(t),++i;n.restore()}}va(t,e,n){if(t&&e){var r=Sa(this);n=Aa(n),r.save();var i=r.canvas,s=0;for(const o of e){r.beginPath(),e=t[o.start];const a=t[o.end];e&&a&&(r.strokeStyle=ba(n.color,{index:s,from:e,to:a}),r.lineWidth=ba(n.lineWidth,{index:s,from:e,to:a}),r.moveTo(e.x*i.width,e.y*i.height),r.lineTo(a.x*i.width,a.y*i.height)),++s,r.stroke()}r.restore()}}sa(t,e){const n=Sa(this);e=Aa(e),n.save(),n.beginPath(),n.lineWidth=ba(e.lineWidth,{}),n.strokeStyle=ba(e.color,{}),n.fillStyle=ba(e.fillColor,{}),n.moveTo(t.originX,t.originY),n.lineTo(t.originX+t.width,t.originY),n.lineTo(t.originX+t.width,t.originY+t.height),n.lineTo(t.originX,t.originY+t.height),n.lineTo(t.originX,t.originY),n.stroke(),n.fill(),n.restore()}ta(t,e,n=[0,0,0,255]){this.l?function(t,e,n,r){const i=xa(t);La(t,e,(e=>{Ra(t,e,n,r),(e=Sa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,n,e):Ra(this,t.N(),n,e)}ua(t,e,n){this.l?function(t,e,n,r){const i=xa(t);La(t,e,(e=>{Fa(t,e,n,r),(e=Sa(t)).drawImage(i.canvas,0,0,e.canvas.width,e.canvas.height)}))}(this,t,e,n):Fa(this,t.N(),e,n)}close(){this.g?.close(),this.g=void 0,this.h?.close(),this.h=void 0,this.m?.close(),this.m=void 0}};function Ma(t,e){switch(e){case 0:return t.g.find((t=>t instanceof ImageData));case 1:return t.g.find((t=>\"undefined\"!=typeof ImageBitmap&&t instanceof ImageBitmap));case 2:return t.g.find((t=>\"undefined\"!=typeof WebGLTexture&&t instanceof WebGLTexture));default:throw Error(`Type is not supported: ${e}`)}}function Pa(t){var e=Ma(t,0);if(!e){e=Oa(t);const n=Ua(t),r=new Uint8Array(t.width*t.height*4);oa(n,e,Ca(t)),e.readPixels(0,0,t.width,t.height,e.RGBA,e.UNSIGNED_BYTE,r),aa(n),e=new ImageData(new Uint8ClampedArray(r.buffer),t.width,t.height),t.g.push(e)}return e}function Ca(t){let e=Ma(t,2);if(!e){const n=Oa(t);e=Da(t);const r=Ma(t,1)||Pa(t);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r),Na(t)}return e}function Oa(t){if(!t.canvas)throw Error(\"Conversion to different image formats require that a canvas is passed when initializing the image.\");return t.h||(t.h=Qo(t.canvas.getContext(\"webgl2\"),\"You cannot use a canvas that is already bound to a different type of rendering context.\")),t.h}function Ua(t){return t.l||(t.l=new ca),t.l}function Da(t){const e=Oa(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=Ma(t,2);return n||(n=sa(Ua(t),e),t.g.push(n),t.m=!0),e.bindTexture(e.TEXTURE_2D,n),n}function Na(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}function Ba(t){const e=Oa(t);return ia(Ua(t),e,!0,(()=>function(t,e){const n=t.canvas;if(n.width===t.width&&n.height===t.height)return e();const r=n.width,i=n.height;return n.width=t.width,n.height=t.height,t=e(),n.width=r,n.height=i,t}(t,(()=>{if(e.bindFramebuffer(e.FRAMEBUFFER,null),e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),e.drawArrays(e.TRIANGLE_FAN,0,4),!(t.canvas instanceof OffscreenCanvas))throw Error(\"Conversion to ImageBitmap requires that the MediaPipe Tasks is initialized with an OffscreenCanvas\");return t.canvas.transferToImageBitmap()}))))}Ia.prototype.close=Ia.prototype.close,Ia.prototype.drawConfidenceMask=Ia.prototype.ua,Ia.prototype.drawCategoryMask=Ia.prototype.ta,Ia.prototype.drawBoundingBox=Ia.prototype.sa,Ia.prototype.drawConnectors=Ia.prototype.va,Ia.prototype.drawLandmarks=Ia.prototype.wa,Ia.lerp=function(t,e,n,r,i){return ka(r*(1-(t-e)/(n-e))+i*(1-(n-t)/(n-e)),r,i)},Ia.clamp=ka;var Ga=class{constructor(t,e,n,r,i,s,o){this.g=t,this.j=e,this.m=n,this.canvas=r,this.l=i,this.width=s,this.height=o,(this.j||this.m)&&(0===--ja&&console.error(\"You seem to be creating MPImage instances without invoking .close(). This leaks resources.\"))}Ca(){return!!Ma(this,0)}ka(){return!!Ma(this,1)}R(){return!!Ma(this,2)}Aa(){return Pa(this)}za(){var t=Ma(this,1);return t||(Ca(this),Da(this),t=Ba(this),Na(this),this.g.push(t),this.j=!0),t}N(){return Ca(this)}clone(){const t=[];for(const e of this.g){let n;if(e instanceof ImageData)n=new ImageData(e.data,this.width,this.height);else if(e instanceof WebGLTexture){const t=Oa(this),e=Ua(this);t.activeTexture(t.TEXTURE1),n=sa(e,t),t.bindTexture(t.TEXTURE_2D,n),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,this.width,this.height,0,t.RGBA,t.UNSIGNED_BYTE,null),t.bindTexture(t.TEXTURE_2D,null),oa(e,t,n),ia(e,t,!1,(()=>{Da(this),t.clearColor(0,0,0,0),t.clear(t.COLOR_BUFFER_BIT),t.drawArrays(t.TRIANGLE_FAN,0,4),Na(this)})),aa(e),Na(this)}else{if(!(e instanceof ImageBitmap))throw Error(`Type is not supported: ${e}`);Ca(this),Da(this),n=Ba(this),Na(this)}t.push(n)}return new Ga(t,this.ka(),this.R(),this.canvas,this.l,this.width,this.height)}close(){this.j&&Ma(this,1).close(),this.m&&Oa(this).deleteTexture(Ma(this,2)),ja=-1}};Ga.prototype.close=Ga.prototype.close,Ga.prototype.clone=Ga.prototype.clone,Ga.prototype.getAsWebGLTexture=Ga.prototype.N,Ga.prototype.getAsImageBitmap=Ga.prototype.za,Ga.prototype.getAsImageData=Ga.prototype.Aa,Ga.prototype.hasWebGLTexture=Ga.prototype.R,Ga.prototype.hasImageBitmap=Ga.prototype.ka,Ga.prototype.hasImageData=Ga.prototype.Ca;var ja=250;function Va(...t){return t.map((([t,e])=>({start:t,end:e})))}const Xa=function(t){return class extends t{Ha(){this.i._registerModelResourcesGraphService()}}}((Ha=class{constructor(t,e){this.l=!0,this.i=t,this.g=null,this.h=0,this.m=\"function\"==typeof this.i._addIntToInputStream,void 0!==e?this.i.canvas=e:Do()?this.i.canvas=new OffscreenCanvas(1,1):(console.warn(\"OffscreenCanvas not supported and GraphRunner constructor glCanvas parameter is undefined. Creating backup canvas.\"),this.i.canvas=document.createElement(\"canvas\"))}async initializeGraph(t){const e=await(await fetch(t)).arrayBuffer();t=!(t.endsWith(\".pbtxt\")||t.endsWith(\".textproto\")),this.setGraph(new Uint8Array(e),t)}setGraphFromString(t){this.setGraph((new TextEncoder).encode(t),!1)}setGraph(t,e){const n=t.length,r=this.i._malloc(n);this.i.HEAPU8.set(t,r),e?this.i._changeBinaryGraph(n,r):this.i._changeTextGraph(n,r),this.i._free(r)}configureAudio(t,e,n,r,i){this.i._configureAudio||console.warn('Attempting to use configureAudio without support for input audio. Is build dep \":gl_graph_runner_audio\" missing?'),Go(this,r||\"input_audio\",(r=>{Go(this,i=i||\"audio_header\",(i=>{this.i._configureAudio(r,i,t,e??0,n)}))}))}setAutoResizeCanvas(t){this.l=t}setAutoRenderToScreen(t){this.i._setAutoRenderToScreen(t)}setGpuBufferVerticalFlip(t){this.i.gpuOriginForWebTexturesIsBottomLeft=t}ca(t){Xo(this,\"__graph_config__\",(e=>{t(e)})),Go(this,\"__graph_config__\",(t=>{this.i._getGraphConfig(t,void 0)})),delete this.i.simpleListeners.__graph_config__}attachErrorListener(t){this.i.errorListener=t}attachEmptyPacketListener(t,e){this.i.emptyPacketListeners=this.i.emptyPacketListeners||{},this.i.emptyPacketListeners[t]=e}addAudioToStream(t,e,n){this.addAudioToStreamWithShape(t,0,0,e,n)}addAudioToStreamWithShape(t,e,n,r,i){const s=4*t.length;this.h!==s&&(this.g&&this.i._free(this.g),this.g=this.i._malloc(s),this.h=s),this.i.HEAPF32.set(t,this.g/4),Go(this,r,(t=>{this.i._addAudioToInputStream(this.g,e,n,t,i)}))}addGpuBufferToStream(t,e,n){Go(this,e,(e=>{const[r,i]=jo(this,t,e);this.i._addBoundTextureToStream(e,r,i,n)}))}addBoolToStream(t,e,n){Go(this,e,(e=>{this.i._addBoolToInputStream(t,e,n)}))}addDoubleToStream(t,e,n){Go(this,e,(e=>{this.i._addDoubleToInputStream(t,e,n)}))}addFloatToStream(t,e,n){Go(this,e,(e=>{this.i._addFloatToInputStream(t,e,n)}))}addIntToStream(t,e,n){Go(this,e,(e=>{this.i._addIntToInputStream(t,e,n)}))}addUintToStream(t,e,n){Go(this,e,(e=>{this.i._addUintToInputStream(t,e,n)}))}addStringToStream(t,e,n){Go(this,e,(e=>{Go(this,t,(t=>{this.i._addStringToInputStream(t,e,n)}))}))}addStringRecordToStream(t,e,n){Go(this,e,(e=>{Vo(this,Object.keys(t),(r=>{Vo(this,Object.values(t),(i=>{this.i._addFlatHashMapToInputStream(r,i,Object.keys(t).length,e,n)}))}))}))}addProtoToStream(t,e,n,r){Go(this,n,(n=>{Go(this,e,(e=>{const i=this.i._malloc(t.length);this.i.HEAPU8.set(t,i),this.i._addProtoToInputStream(i,t.length,e,n,r),this.i._free(i)}))}))}addEmptyPacketToStream(t,e){Go(this,t,(t=>{this.i._addEmptyPacketToInputStream(t,e)}))}addBoolVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateBoolVector(t.length);if(!r)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(r,e);this.i._addBoolVectorToInputStream(r,e,n)}))}addDoubleVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateDoubleVector(t.length);if(!r)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(r,e);this.i._addDoubleVectorToInputStream(r,e,n)}))}addFloatVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateFloatVector(t.length);if(!r)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(r,e);this.i._addFloatVectorToInputStream(r,e,n)}))}addIntVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateIntVector(t.length);if(!r)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(r,e);this.i._addIntVectorToInputStream(r,e,n)}))}addUintVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateUintVector(t.length);if(!r)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(r,e);this.i._addUintVectorToInputStream(r,e,n)}))}addStringVectorToStream(t,e,n){Go(this,e,(e=>{const r=this.i._allocateStringVector(t.length);if(!r)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)Go(this,e,(t=>{this.i._addStringVectorEntry(r,t)}));this.i._addStringVectorToInputStream(r,e,n)}))}addBoolToInputSidePacket(t,e){Go(this,e,(e=>{this.i._addBoolToInputSidePacket(t,e)}))}addDoubleToInputSidePacket(t,e){Go(this,e,(e=>{this.i._addDoubleToInputSidePacket(t,e)}))}addFloatToInputSidePacket(t,e){Go(this,e,(e=>{this.i._addFloatToInputSidePacket(t,e)}))}addIntToInputSidePacket(t,e){Go(this,e,(e=>{this.i._addIntToInputSidePacket(t,e)}))}addUintToInputSidePacket(t,e){Go(this,e,(e=>{this.i._addUintToInputSidePacket(t,e)}))}addStringToInputSidePacket(t,e){Go(this,e,(e=>{Go(this,t,(t=>{this.i._addStringToInputSidePacket(t,e)}))}))}addProtoToInputSidePacket(t,e,n){Go(this,n,(n=>{Go(this,e,(e=>{const r=this.i._malloc(t.length);this.i.HEAPU8.set(t,r),this.i._addProtoToInputSidePacket(r,t.length,e,n),this.i._free(r)}))}))}addBoolVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateBoolVector(t.length);if(!n)throw Error(\"Unable to allocate new bool vector on heap.\");for(const e of t)this.i._addBoolVectorEntry(n,e);this.i._addBoolVectorToInputSidePacket(n,e)}))}addDoubleVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateDoubleVector(t.length);if(!n)throw Error(\"Unable to allocate new double vector on heap.\");for(const e of t)this.i._addDoubleVectorEntry(n,e);this.i._addDoubleVectorToInputSidePacket(n,e)}))}addFloatVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateFloatVector(t.length);if(!n)throw Error(\"Unable to allocate new float vector on heap.\");for(const e of t)this.i._addFloatVectorEntry(n,e);this.i._addFloatVectorToInputSidePacket(n,e)}))}addIntVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateIntVector(t.length);if(!n)throw Error(\"Unable to allocate new int vector on heap.\");for(const e of t)this.i._addIntVectorEntry(n,e);this.i._addIntVectorToInputSidePacket(n,e)}))}addUintVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateUintVector(t.length);if(!n)throw Error(\"Unable to allocate new unsigned int vector on heap.\");for(const e of t)this.i._addUintVectorEntry(n,e);this.i._addUintVectorToInputSidePacket(n,e)}))}addStringVectorToInputSidePacket(t,e){Go(this,e,(e=>{const n=this.i._allocateStringVector(t.length);if(!n)throw Error(\"Unable to allocate new string vector on heap.\");for(const e of t)Go(this,e,(t=>{this.i._addStringVectorEntry(n,t)}));this.i._addStringVectorToInputSidePacket(n,e)}))}attachBoolListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachBoolListener(t)}))}attachBoolVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachBoolVectorListener(t)}))}attachIntListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachIntListener(t)}))}attachIntVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachIntVectorListener(t)}))}attachUintListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachUintListener(t)}))}attachUintVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachUintVectorListener(t)}))}attachDoubleListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachDoubleListener(t)}))}attachDoubleVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachDoubleVectorListener(t)}))}attachFloatListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachFloatListener(t)}))}attachFloatVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachFloatVectorListener(t)}))}attachStringListener(t,e){Xo(this,t,e),Go(this,t,(t=>{this.i._attachStringListener(t)}))}attachStringVectorListener(t,e){Ho(this,t,e),Go(this,t,(t=>{this.i._attachStringVectorListener(t)}))}attachProtoListener(t,e,n){Xo(this,t,e),Go(this,t,(t=>{this.i._attachProtoListener(t,n||!1)}))}attachProtoVectorListener(t,e,n){Ho(this,t,e),Go(this,t,(t=>{this.i._attachProtoVectorListener(t,n||!1)}))}attachAudioListener(t,e,n){this.i._attachAudioListener||console.warn('Attempting to use attachAudioListener without support for output audio. Is build dep \":gl_graph_runner_audio_out\" missing?'),Xo(this,t,((t,n)=>{t=new Float32Array(t.buffer,t.byteOffset,t.length/4),e(t,n)})),Go(this,t,(t=>{this.i._attachAudioListener(t,n||!1)}))}finishProcessing(){this.i._waitUntilIdle()}closeGraph(){this.i._closeGraph(),this.i.simpleListeners=void 0,this.i.emptyPacketListeners=void 0}},class extends Ha{get ea(){return this.i}oa(t,e,n){Go(this,e,(e=>{const[r,i]=jo(this,t,e);this.ea._addBoundTextureAsImageToStream(e,r,i,n)}))}V(t,e){Xo(this,t,e),Go(this,t,(t=>{this.ea._attachImageListener(t)}))}ba(t,e){Ho(this,t,e),Go(this,t,(t=>{this.ea._attachImageVectorListener(t)}))}}));var Ha,Wa=class extends Xa{};async function za(t,e,n){return async function(t,e,n,r){return Wo(t,e,n,r)}(t,n.canvas??(Do()?void 0:document.createElement(\"canvas\")),e,n)}function Ka(t,e,n,r){if(t.U){const s=new ms;if(n?.regionOfInterest){if(!t.na)throw Error(\"This task doesn't support region-of-interest.\");var i=n.regionOfInterest;if(i.left>=i.right||i.top>=i.bottom)throw Error(\"Expected RectF with left < right and top < bottom.\");if(i.left<0||i.top<0||i.right>1||i.bottom>1)throw Error(\"Expected RectF values to be in [0,1].\");An(s,1,(i.left+i.right)/2),An(s,2,(i.top+i.bottom)/2),An(s,4,i.right-i.left),An(s,3,i.bottom-i.top)}else An(s,1,.5),An(s,2,.5),An(s,4,1),An(s,3,1);if(n?.rotationDegrees){if(n?.rotationDegrees%90!=0)throw Error(\"Expected rotation to be a multiple of 90°.\");if(An(s,5,-Math.PI*n.rotationDegrees/180),n?.rotationDegrees%180!=0){const[t,r]=Bo(e);n=En(s,3)*r/t,i=En(s,4)*t/r,An(s,4,n),An(s,3,i)}}t.g.addProtoToStream(s.g(),\"mediapipe.NormalizedRect\",t.U,r)}t.g.oa(e,t.Z,r??performance.now()),t.finishProcessing()}function Ya(t,e,n){if(t.baseOptions?.g())throw Error(\"Task is not initialized with image mode. 'runningMode' must be set to 'IMAGE'.\");Ka(t,e,n,t.B+1)}function $a(t,e,n,r){if(!t.baseOptions?.g())throw Error(\"Task is not initialized with video mode. 'runningMode' must be set to 'VIDEO'.\");Ka(t,e,n,r)}function qa(t,e,n,r){var i=e.data;const s=e.width,o=s*(e=e.height);if((i instanceof Uint8Array||i instanceof Float32Array)&&i.length!==o)throw Error(\"Unsupported channel count: \"+i.length/o);return t=new Ea([i],n,!1,t.g.i.canvas,t.P,s,e),r?t.clone():t}var Ja=class extends Zo{constructor(t,e,n,r){super(t),this.g=t,this.Z=e,this.U=n,this.na=r,this.P=new ca}l(t,e=!0){if(\"runningMode\"in t&&wn(this.baseOptions,2,!!t.runningMode&&\"IMAGE\"!==t.runningMode),void 0!==t.canvas&&this.g.i.canvas!==t.canvas)throw Error(\"You must create a new task to reset the canvas.\");return super.l(t,e)}close(){this.P.close(),super.close()}};Ja.prototype.close=Ja.prototype.close;var Za=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect_in\",!1),this.j={detections:[]},dn(t=this.h=new Cs,0,1,e=new Is),An(this.h,2,.5),An(this.h,3,.3)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return\"minDetectionConfidence\"in t&&An(this.h,2,t.minDetectionConfidence??.5),\"minSuppressionThreshold\"in t&&An(this.h,3,t.minSuppressionThreshold??.3),this.l(t)}D(t,e){return this.j={detections:[]},Ya(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},$a(this,t,n,e),this.j}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect_in\"),Zi(t,\"detections\");const e=new Gi;Yn(e,Us,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.face_detector.FaceDetectorGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect_in\"),Wi(n,\"DETECTIONS:detections\"),n.o(e),qi(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=hs(e),this.j.detections.push(xo(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Za.prototype.detectForVideo=Za.prototype.F,Za.prototype.detect=Za.prototype.D,Za.prototype.setOptions=Za.prototype.o,Za.createFromModelPath=async function(t,e){return za(Za,t,{baseOptions:{modelAssetPath:e}})},Za.createFromModelBuffer=function(t,e){return za(Za,t,{baseOptions:{modelAssetBuffer:e}})},Za.createFromOptions=function(t,e){return za(Za,t,e)};var Qa=Va([61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]),tc=Va([263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]),ec=Va([276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]),nc=Va([474,475],[475,476],[476,477],[477,474]),rc=Va([33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]),ic=Va([46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]),sc=Va([469,470],[470,471],[471,472],[472,469]),oc=Va([10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]),ac=[...Qa,...tc,...ec,...rc,...ic,...oc],cc=Va([127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]);function hc(t){t.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]}}var uc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!1),this.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]},this.outputFacialTransformationMatrixes=this.outputFaceBlendshapes=!1,dn(t=this.h=new Bs,0,1,e=new Is),this.v=new Ns,dn(this.h,0,3,this.v),this.s=new Cs,dn(this.h,0,2,this.s),Tn(this.s,4,1),An(this.s,2,.5),An(this.v,2,.5),An(this.h,4,.5)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return\"numFaces\"in t&&Tn(this.s,4,t.numFaces??1),\"minFaceDetectionConfidence\"in t&&An(this.s,2,t.minFaceDetectionConfidence??.5),\"minTrackingConfidence\"in t&&An(this.h,4,t.minTrackingConfidence??.5),\"minFacePresenceConfidence\"in t&&An(this.v,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"outputFacialTransformationMatrixes\"in t&&(this.outputFacialTransformationMatrixes=!!t.outputFacialTransformationMatrixes),this.l(t)}D(t,e){return hc(this),Ya(this,t,e),this.j}F(t,e,n){return hc(this),$a(this,t,n,e),this.j}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"face_landmarks\");const e=new Gi;Yn(e,Vs,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.face_landmarker.FaceLandmarkerGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"NORM_LANDMARKS:face_landmarks\"),n.o(e),qi(t,n),this.g.attachProtoVectorListener(\"face_landmarks\",((t,e)=>{for(const e of t)t=fs(e),this.j.faceLandmarks.push(Lo(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{Yo(this,t)})),this.outputFaceBlendshapes&&(Zi(t,\"blendshapes\"),Wi(n,\"BLENDSHAPES:blendshapes\"),this.g.attachProtoVectorListener(\"blendshapes\",((t,e)=>{if(this.outputFaceBlendshapes)for(const e of t)t=ss(e),this.j.faceBlendshapes.push(So(t.g()??[]));Yo(this,e)})),this.g.attachEmptyPacketListener(\"blendshapes\",(t=>{Yo(this,t)}))),this.outputFacialTransformationMatrixes&&(Zi(t,\"face_geometry\"),Wi(n,\"FACE_GEOMETRY:face_geometry\"),this.g.attachProtoVectorListener(\"face_geometry\",((t,e)=>{if(this.outputFacialTransformationMatrixes)for(const e of t)(t=hn(Ds(e),ps,2))&&this.j.facialTransformationMatrixes.push({rows:_n(t,1)??0??0,columns:_n(t,2)??0??0,data:$e(t,3,qt,Ye()).slice()??[]});Yo(this,e)})),this.g.attachEmptyPacketListener(\"face_geometry\",(t=>{Yo(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};uc.prototype.detectForVideo=uc.prototype.F,uc.prototype.detect=uc.prototype.D,uc.prototype.setOptions=uc.prototype.o,uc.createFromModelPath=function(t,e){return za(uc,t,{baseOptions:{modelAssetPath:e}})},uc.createFromModelBuffer=function(t,e){return za(uc,t,{baseOptions:{modelAssetBuffer:e}})},uc.createFromOptions=function(t,e){return za(uc,t,e)},uc.FACE_LANDMARKS_LIPS=Qa,uc.FACE_LANDMARKS_LEFT_EYE=tc,uc.FACE_LANDMARKS_LEFT_EYEBROW=ec,uc.FACE_LANDMARKS_LEFT_IRIS=nc,uc.FACE_LANDMARKS_RIGHT_EYE=rc,uc.FACE_LANDMARKS_RIGHT_EYEBROW=ic,uc.FACE_LANDMARKS_RIGHT_IRIS=sc,uc.FACE_LANDMARKS_FACE_OVAL=oc,uc.FACE_LANDMARKS_CONTOURS=ac,uc.FACE_LANDMARKS_TESSELATION=cc;var lc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!0),dn(t=this.j=new Xs,0,1,e=new Is)}get baseOptions(){return hn(this.j,Is,1)}set baseOptions(t){dn(this.j,0,1,t)}o(t){return super.l(t)}Ka(t,e,n){const r=\"function\"!=typeof e?e:{};if(this.h=\"function\"==typeof e?e:n,Ya(this,t,r??{}),!this.h)return this.s}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"stylized_image\");const e=new Gi;Yn(e,Hs,this.j);const n=new zi;Xi(n,\"mediapipe.tasks.vision.face_stylizer.FaceStylizerGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"STYLIZED_IMAGE:stylized_image\"),n.o(e),qi(t,n),this.g.V(\"stylized_image\",((t,e)=>{var n=!this.h,r=t.data,i=t.width;const s=i*(t=t.height);if(r instanceof Uint8Array)if(r.length===3*s){const e=new Uint8ClampedArray(4*s);for(let t=0;t<s;++t)e[4*t]=r[3*t],e[4*t+1]=r[3*t+1],e[4*t+2]=r[3*t+2],e[4*t+3]=255;r=new ImageData(e,i,t)}else{if(r.length!==4*s)throw Error(\"Unsupported channel count: \"+r.length/s);r=new ImageData(new Uint8ClampedArray(r.buffer,r.byteOffset,r.length),i,t)}else if(!(r instanceof WebGLTexture))throw Error(`Unsupported format: ${r.constructor.name}`);i=new Ga([r],!1,!1,this.g.i.canvas,this.P,i,t),this.s=n=n?i.clone():i,this.h&&this.h(n),Yo(this,e)})),this.g.attachEmptyPacketListener(\"stylized_image\",(t=>{this.s=null,this.h&&this.h(null),Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};lc.prototype.stylize=lc.prototype.Ka,lc.prototype.setOptions=lc.prototype.o,lc.createFromModelPath=function(t,e){return za(lc,t,{baseOptions:{modelAssetPath:e}})},lc.createFromModelBuffer=function(t,e){return za(lc,t,{baseOptions:{modelAssetBuffer:e}})},lc.createFromOptions=function(t,e){return za(lc,t,e)};var dc=Va([0,1],[1,2],[2,3],[3,4],[0,5],[5,6],[6,7],[7,8],[5,9],[9,10],[10,11],[11,12],[9,13],[13,14],[14,15],[15,16],[13,17],[0,17],[17,18],[18,19],[19,20]);function fc(t){t.gestures=[],t.landmarks=[],t.worldLandmarks=[],t.handedness=[]}function pc(t){return 0===t.gestures.length?{gestures:[],landmarks:[],worldLandmarks:[],handedness:[],handednesses:[]}:{gestures:t.gestures,landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handedness:t.handedness,handednesses:t.handedness}}function gc(t,e=!0){const n=[];for(const i of t){var r=ss(i);t=[];for(const n of r.g())r=e&&null!=_n(n,1)?_n(n,1)??0:-1,t.push({score:En(n,2)??0,index:r,categoryName:vn(n,3)??\"\"??\"\",displayName:vn(n,4)??\"\"??\"\"});n.push(t)}return n}var mc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!1),this.gestures=[],this.landmarks=[],this.worldLandmarks=[],this.handedness=[],dn(t=this.j=new Js,0,1,e=new Is),this.s=new qs,dn(this.j,0,2,this.s),this.C=new $s,dn(this.s,0,3,this.C),this.v=new Ys,dn(this.s,0,2,this.v),this.h=new Ks,dn(this.j,0,3,this.h),An(this.v,2,.5),An(this.s,4,.5),An(this.C,2,.5)}get baseOptions(){return hn(this.j,Is,1)}set baseOptions(t){dn(this.j,0,1,t)}o(t){if(Tn(this.v,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&An(this.v,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&An(this.s,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&An(this.C,2,t.minHandPresenceConfidence??.5),t.cannedGesturesClassifierOptions){var e=new Ws,n=e,r=ko(t.cannedGesturesClassifierOptions,hn(this.h,Ws,3)?.h());dn(n,0,2,r),dn(this.h,0,3,e)}else void 0===t.cannedGesturesClassifierOptions&&hn(this.h,Ws,3)?.g();return t.customGesturesClassifierOptions?(dn(n=e=new Ws,0,2,r=ko(t.customGesturesClassifierOptions,hn(this.h,Ws,4)?.h())),dn(this.h,0,4,e)):void 0===t.customGesturesClassifierOptions&&hn(this.h,Ws,4)?.g(),this.l(t)}Fa(t,e){return fc(this),Ya(this,t,e),pc(this)}Ga(t,e,n){return fc(this),$a(this,t,n,e),pc(this)}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"hand_gestures\"),Zi(t,\"hand_landmarks\"),Zi(t,\"world_hand_landmarks\"),Zi(t,\"handedness\");const e=new Gi;Yn(e,no,this.j);const n=new zi;Xi(n,\"mediapipe.tasks.vision.gesture_recognizer.GestureRecognizerGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"HAND_GESTURES:hand_gestures\"),Wi(n,\"LANDMARKS:hand_landmarks\"),Wi(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),Wi(n,\"HANDEDNESS:handedness\"),n.o(e),qi(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t){t=fs(e);const n=[];for(const e of ln(t,ds,1))n.push({x:En(e,1)??0,y:En(e,2)??0,z:En(e,3)??0,visibility:En(e,4)??0});this.landmarks.push(n)}Yo(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t){t=ls(e);const n=[];for(const e of ln(t,us,1))n.push({x:En(e,1)??0,y:En(e,2)??0,z:En(e,3)??0,visibility:En(e,4)??0});this.worldLandmarks.push(n)}Yo(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoVectorListener(\"hand_gestures\",((t,e)=>{this.gestures.push(...gc(t,!1)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"hand_gestures\",(t=>{Yo(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{this.handedness.push(...gc(t)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};function yc(t){return{landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handednesses:t.handedness,handedness:t.handedness}}mc.prototype.recognizeForVideo=mc.prototype.Ga,mc.prototype.recognize=mc.prototype.Fa,mc.prototype.setOptions=mc.prototype.o,mc.createFromModelPath=function(t,e){return za(mc,t,{baseOptions:{modelAssetPath:e}})},mc.createFromModelBuffer=function(t,e){return za(mc,t,{baseOptions:{modelAssetBuffer:e}})},mc.createFromOptions=function(t,e){return za(mc,t,e)},mc.HAND_CONNECTIONS=dc;var _c=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.handedness=[],dn(t=this.h=new qs,0,1,e=new Is),this.s=new $s,dn(this.h,0,3,this.s),this.j=new Ys,dn(this.h,0,2,this.j),Tn(this.j,3,1),An(this.j,2,.5),An(this.s,2,.5),An(this.h,4,.5)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return\"numHands\"in t&&Tn(this.j,3,t.numHands??1),\"minHandDetectionConfidence\"in t&&An(this.j,2,t.minHandDetectionConfidence??.5),\"minTrackingConfidence\"in t&&An(this.h,4,t.minTrackingConfidence??.5),\"minHandPresenceConfidence\"in t&&An(this.s,2,t.minHandPresenceConfidence??.5),this.l(t)}D(t,e){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],Ya(this,t,e),yc(this)}F(t,e,n){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],$a(this,t,n,e),yc(this)}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"hand_landmarks\"),Zi(t,\"world_hand_landmarks\"),Zi(t,\"handedness\");const e=new Gi;Yn(e,ro,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.hand_landmarker.HandLandmarkerGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"LANDMARKS:hand_landmarks\"),Wi(n,\"WORLD_LANDMARKS:world_hand_landmarks\"),Wi(n,\"HANDEDNESS:handedness\"),n.o(e),qi(t,n),this.g.attachProtoVectorListener(\"hand_landmarks\",((t,e)=>{for(const e of t)t=fs(e),this.landmarks.push(Lo(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoVectorListener(\"world_hand_landmarks\",((t,e)=>{for(const e of t)t=ls(e),this.worldLandmarks.push(Ro(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"world_hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoVectorListener(\"handedness\",((t,e)=>{var n=this.handedness,r=n.push;const i=[];for(const e of t){t=ss(e);const n=[];for(const e of t.g())n.push({score:En(e,2)??0,index:_n(e,1)??0??-1,categoryName:vn(e,3)??\"\"??\"\",displayName:vn(e,4)??\"\"??\"\"});i.push(n)}r.call(n,...i),Yo(this,e)})),this.g.attachEmptyPacketListener(\"handedness\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};_c.prototype.detectForVideo=_c.prototype.F,_c.prototype.detect=_c.prototype.D,_c.prototype.setOptions=_c.prototype.o,_c.createFromModelPath=function(t,e){return za(_c,t,{baseOptions:{modelAssetPath:e}})},_c.createFromModelBuffer=function(t,e){return za(_c,t,{baseOptions:{modelAssetBuffer:e}})},_c.createFromOptions=function(t,e){return za(_c,t,e)},_c.HAND_CONNECTIONS=dc;var vc=Va([0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]);function Ec(t){t.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]}}function wc(t){try{if(!t.C)return t.h;t.C(t.h)}finally{Jo(t)}}function Tc(t,e){t=fs(t),e.push(Lo(t))}var Ac=class extends Ja{constructor(t,e){super(new Wa(t,e),\"input_frames_image\",null,!1),this.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]},this.outputPoseSegmentationMasks=this.outputFaceBlendshapes=!1,dn(t=this.j=new ao,0,1,e=new Is),this.K=new $s,dn(this.j,0,2,this.K),this.Y=new io,dn(this.j,0,3,this.Y),this.s=new Cs,dn(this.j,0,4,this.s),this.H=new Ns,dn(this.j,0,5,this.H),this.v=new so,dn(this.j,0,6,this.v),this.L=new oo,dn(this.j,0,7,this.L),An(this.s,2,.5),An(this.s,3,.3),An(this.H,2,.5),An(this.v,2,.5),An(this.v,3,.3),An(this.L,2,.5),An(this.K,2,.5)}get baseOptions(){return hn(this.j,Is,1)}set baseOptions(t){dn(this.j,0,1,t)}o(t){return\"minFaceDetectionConfidence\"in t&&An(this.s,2,t.minFaceDetectionConfidence??.5),\"minFaceSuppressionThreshold\"in t&&An(this.s,3,t.minFaceSuppressionThreshold??.3),\"minFacePresenceConfidence\"in t&&An(this.H,2,t.minFacePresenceConfidence??.5),\"outputFaceBlendshapes\"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),\"minPoseDetectionConfidence\"in t&&An(this.v,2,t.minPoseDetectionConfidence??.5),\"minPoseSuppressionThreshold\"in t&&An(this.v,3,t.minPoseSuppressionThreshold??.3),\"minPosePresenceConfidence\"in t&&An(this.L,2,t.minPosePresenceConfidence??.5),\"outputPoseSegmentationMasks\"in t&&(this.outputPoseSegmentationMasks=!!t.outputPoseSegmentationMasks),\"minHandLandmarksConfidence\"in t&&An(this.K,2,t.minHandLandmarksConfidence??.5),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.C=\"function\"==typeof e?e:n,Ec(this),Ya(this,t,r),wc(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.C=\"function\"==typeof n?n:r,Ec(this),$a(this,t,i,e),wc(this)}m(){var t=new Qi;Ji(t,\"input_frames_image\"),Zi(t,\"pose_landmarks\"),Zi(t,\"pose_world_landmarks\"),Zi(t,\"face_landmarks\"),Zi(t,\"left_hand_landmarks\"),Zi(t,\"left_hand_world_landmarks\"),Zi(t,\"right_hand_landmarks\"),Zi(t,\"right_hand_world_landmarks\");const e=new Gi,n=new xi;tn(n,1,de(\"type.googleapis.com/mediapipe.tasks.vision.holistic_landmarker.proto.HolisticLandmarkerGraphOptions\"),\"\"),function(t,e){if(null!=e)if(Array.isArray(e))He(t,2,Pe(e,Oe,void 0,void 0,!1));else{if(!(\"string\"==typeof e||e instanceof N||C(e)))throw Error(\"invalid value in Any.value field: \"+e+\" expected a ByteString, a base64 encoded string, a Uint8Array or a jspb array\");tn(t,2,dt(e,!1),U())}}(n,this.j.g());const r=new zi;Xi(r,\"mediapipe.tasks.vision.holistic_landmarker.HolisticLandmarkerGraph\"),yn(r,8,xi,n),Hi(r,\"IMAGE:input_frames_image\"),Wi(r,\"POSE_LANDMARKS:pose_landmarks\"),Wi(r,\"POSE_WORLD_LANDMARKS:pose_world_landmarks\"),Wi(r,\"FACE_LANDMARKS:face_landmarks\"),Wi(r,\"LEFT_HAND_LANDMARKS:left_hand_landmarks\"),Wi(r,\"LEFT_HAND_WORLD_LANDMARKS:left_hand_world_landmarks\"),Wi(r,\"RIGHT_HAND_LANDMARKS:right_hand_landmarks\"),Wi(r,\"RIGHT_HAND_WORLD_LANDMARKS:right_hand_world_landmarks\"),r.o(e),qi(t,r),$o(this,t),this.g.attachProtoListener(\"pose_landmarks\",((t,e)=>{Tc(t,this.h.poseLandmarks),Yo(this,e)})),this.g.attachEmptyPacketListener(\"pose_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoListener(\"pose_world_landmarks\",((t,e)=>{var n=this.h.poseWorldLandmarks;t=ls(t),n.push(Ro(t)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"pose_world_landmarks\",(t=>{Yo(this,t)})),this.outputPoseSegmentationMasks&&(Wi(r,\"POSE_SEGMENTATION_MASK:pose_segmentation_mask\"),qo(this,\"pose_segmentation_mask\"),this.g.V(\"pose_segmentation_mask\",((t,e)=>{this.h.poseSegmentationMasks=[qa(this,t,!0,!this.C)],Yo(this,e)})),this.g.attachEmptyPacketListener(\"pose_segmentation_mask\",(t=>{this.h.poseSegmentationMasks=[],Yo(this,t)}))),this.g.attachProtoListener(\"face_landmarks\",((t,e)=>{Tc(t,this.h.faceLandmarks),Yo(this,e)})),this.g.attachEmptyPacketListener(\"face_landmarks\",(t=>{Yo(this,t)})),this.outputFaceBlendshapes&&(Zi(t,\"extra_blendshapes\"),Wi(r,\"FACE_BLENDSHAPES:extra_blendshapes\"),this.g.attachProtoListener(\"extra_blendshapes\",((t,e)=>{var n=this.h.faceBlendshapes;this.outputFaceBlendshapes&&(t=ss(t),n.push(So(t.g()??[]))),Yo(this,e)})),this.g.attachEmptyPacketListener(\"extra_blendshapes\",(t=>{Yo(this,t)}))),this.g.attachProtoListener(\"left_hand_landmarks\",((t,e)=>{Tc(t,this.h.leftHandLandmarks),Yo(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoListener(\"left_hand_world_landmarks\",((t,e)=>{var n=this.h.leftHandWorldLandmarks;t=ls(t),n.push(Ro(t)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"left_hand_world_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoListener(\"right_hand_landmarks\",((t,e)=>{Tc(t,this.h.rightHandLandmarks),Yo(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_landmarks\",(t=>{Yo(this,t)})),this.g.attachProtoListener(\"right_hand_world_landmarks\",((t,e)=>{var n=this.h.rightHandWorldLandmarks;t=ls(t),n.push(Ro(t)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"right_hand_world_landmarks\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Ac.prototype.detectForVideo=Ac.prototype.F,Ac.prototype.detect=Ac.prototype.D,Ac.prototype.setOptions=Ac.prototype.o,Ac.createFromModelPath=function(t,e){return za(Ac,t,{baseOptions:{modelAssetPath:e}})},Ac.createFromModelBuffer=function(t,e){return za(Ac,t,{baseOptions:{modelAssetBuffer:e}})},Ac.createFromOptions=function(t,e){return za(Ac,t,e)},Ac.HAND_CONNECTIONS=dc,Ac.POSE_CONNECTIONS=vc,Ac.FACE_LANDMARKS_LIPS=Qa,Ac.FACE_LANDMARKS_LEFT_EYE=tc,Ac.FACE_LANDMARKS_LEFT_EYEBROW=ec,Ac.FACE_LANDMARKS_LEFT_IRIS=nc,Ac.FACE_LANDMARKS_RIGHT_EYE=rc,Ac.FACE_LANDMARKS_RIGHT_EYEBROW=ic,Ac.FACE_LANDMARKS_RIGHT_IRIS=sc,Ac.FACE_LANDMARKS_FACE_OVAL=oc,Ac.FACE_LANDMARKS_CONTOURS=ac,Ac.FACE_LANDMARKS_TESSELATION=cc;var bc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"input_image\",\"norm_rect\",!0),this.j={classifications:[]},dn(t=this.h=new uo,0,1,e=new Is)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return dn(this.h,0,2,ko(t,hn(this.h,bs,2))),this.l(t)}qa(t,e){return this.j={classifications:[]},Ya(this,t,e),this.j}ra(t,e,n){return this.j={classifications:[]},$a(this,t,n,e),this.j}m(){var t=new Qi;Ji(t,\"input_image\"),Ji(t,\"norm_rect\"),Zi(t,\"classifications\");const e=new Gi;Yn(e,lo,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.image_classifier.ImageClassifierGraph\"),Hi(n,\"IMAGE:input_image\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"CLASSIFICATIONS:classifications\"),n.o(e),qi(t,n),this.g.attachProtoListener(\"classifications\",((t,e)=>{this.j=function(t){const e={classifications:ln(t,ys,1).map((t=>So(hn(t,rs,4)?.g()??[],_n(t,2)??0,vn(t,3)??\"\")))};return null!=he(Ve(t,2))&&(e.timestampMs=he(Ve(t,2))??0),e}(_s(t)),Yo(this,e)})),this.g.attachEmptyPacketListener(\"classifications\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};bc.prototype.classifyForVideo=bc.prototype.ra,bc.prototype.classify=bc.prototype.qa,bc.prototype.setOptions=bc.prototype.o,bc.createFromModelPath=function(t,e){return za(bc,t,{baseOptions:{modelAssetPath:e}})},bc.createFromModelBuffer=function(t,e){return za(bc,t,{baseOptions:{modelAssetBuffer:e}})},bc.createFromOptions=function(t,e){return za(bc,t,e)};var kc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!0),this.h=new fo,this.embeddings={embeddings:[]},dn(t=this.h,0,1,e=new Is)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){var e=this.h,n=hn(this.h,Ss,2);return n=n?n.clone():new Ss,void 0!==t.l2Normalize?wn(n,1,t.l2Normalize):\"l2Normalize\"in t&&He(n,1),void 0!==t.quantize?wn(n,2,t.quantize):\"quantize\"in t&&He(n,2),dn(e,0,2,n),this.l(t)}xa(t,e){return Ya(this,t,e),this.embeddings}ya(t,e,n){return $a(this,t,n,e),this.embeddings}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"embeddings_out\");const e=new Gi;Yn(e,po,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.image_embedder.ImageEmbedderGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"EMBEDDINGS:embeddings_out\"),n.o(e),qi(t,n),this.g.attachProtoListener(\"embeddings_out\",((t,e)=>{t=As(t),this.embeddings=function(t){return{embeddings:ln(t,ws,1).map((t=>{const e={headIndex:_n(t,3)??0??-1,headName:vn(t,4)??\"\"??\"\"};if(void 0!==cn(t,vs,nn(t,1)))t=$e(t=hn(t,vs,nn(t,1)),1,qt,Ye()),e.floatEmbedding=t.slice();else{const n=new Uint8Array(0);e.quantizedEmbedding=hn(t,Es,nn(t,2))?.ma()?.h()??n}return e})),timestampMs:he(Ve(t,2))??0}}(t),Yo(this,e)})),this.g.attachEmptyPacketListener(\"embeddings_out\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};kc.cosineSimilarity=function(t,e){if(t.floatEmbedding&&e.floatEmbedding)t=Io(t.floatEmbedding,e.floatEmbedding);else{if(!t.quantizedEmbedding||!e.quantizedEmbedding)throw Error(\"Cannot compute cosine similarity between quantized and float embeddings.\");t=Io(Fo(t.quantizedEmbedding),Fo(e.quantizedEmbedding))}return t},kc.prototype.embedForVideo=kc.prototype.ya,kc.prototype.embed=kc.prototype.xa,kc.prototype.setOptions=kc.prototype.o,kc.createFromModelPath=function(t,e){return za(kc,t,{baseOptions:{modelAssetPath:e}})},kc.createFromModelBuffer=function(t,e){return za(kc,t,{baseOptions:{modelAssetBuffer:e}})},kc.createFromOptions=function(t,e){return za(kc,t,e)};var Sc=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};function xc(t){t.categoryMask=void 0,t.confidenceMasks=void 0,t.qualityScores=void 0}function Lc(t){try{const e=new Sc(t.confidenceMasks,t.categoryMask,t.qualityScores);if(!t.j)return e;t.j(e)}finally{Jo(t)}}Sc.prototype.close=Sc.prototype.close;var Rc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!1),this.s=[],this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new vo,this.v=new go,dn(this.h,0,3,this.v),dn(t=this.h,0,1,e=new Is)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?He(this.h,2,de(t.displayNamesLocale)):\"displayNamesLocale\"in t&&He(this.h,2),\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}J(){!function(t){const e=ln(t.ca(),zi,1).filter((t=>(vn(t,1)??\"\").includes(\"mediapipe.tasks.TensorsToSegmentationCalculator\")));if(t.s=[],e.length>1)throw Error(\"The graph has more than one mediapipe.tasks.TensorsToSegmentationCalculator.\");1===e.length&&(hn(e[0],Gi,7)?.l()?.g()??new Map).forEach(((e,n)=>{t.s[Number(n)]=vn(e,1)??\"\"}))}(this)}segment(t,e,n){const r=\"function\"!=typeof e?e:{};return this.j=\"function\"==typeof e?e:n,xc(this),Ya(this,t,r),Lc(this)}Ia(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.j=\"function\"==typeof n?n:r,xc(this),$a(this,t,i,e),Lc(this)}Ba(){return this.s}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\");const e=new Gi;Yn(e,Eo,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.image_segmenter.ImageSegmenterGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),n.o(e),qi(t,n),$o(this,t),this.outputConfidenceMasks&&(Zi(t,\"confidence_masks\"),Wi(n,\"CONFIDENCE_MASKS:confidence_masks\"),qo(this,\"confidence_masks\"),this.g.ba(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>qa(this,t,!0,!this.j))),Yo(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],Yo(this,t)}))),this.outputCategoryMask&&(Zi(t,\"category_mask\"),Wi(n,\"CATEGORY_MASK:category_mask\"),qo(this,\"category_mask\"),this.g.V(\"category_mask\",((t,e)=>{this.categoryMask=qa(this,t,!1,!this.j),Yo(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,Yo(this,t)}))),Zi(t,\"quality_scores\"),Wi(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,Yo(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Rc.prototype.getLabels=Rc.prototype.Ba,Rc.prototype.segmentForVideo=Rc.prototype.Ia,Rc.prototype.segment=Rc.prototype.segment,Rc.prototype.setOptions=Rc.prototype.o,Rc.createFromModelPath=function(t,e){return za(Rc,t,{baseOptions:{modelAssetPath:e}})},Rc.createFromModelBuffer=function(t,e){return za(Rc,t,{baseOptions:{modelAssetBuffer:e}})},Rc.createFromOptions=function(t,e){return za(Rc,t,e)};var Fc=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){this.confidenceMasks?.forEach((t=>{t.close()})),this.categoryMask?.close()}};Fc.prototype.close=Fc.prototype.close;var Ic=class extends $n{constructor(t){super(t)}},Mc=[0,hi,-2],Pc=[0,ni,-3,di,ni,-1],Cc=[0,Pc],Oc=[0,Pc,hi,-1],Uc=class extends $n{constructor(t){super(t)}},Dc=[0,ni,-1,di],Nc=class extends $n{constructor(t){super(t)}},Bc=class extends $n{constructor(t){super(t)}},Gc=[1,2,3,4,5,6,7,8,9,10,14,15],jc=class extends $n{constructor(t){super(t)}};jc.prototype.g=Si([0,yi,[0,Gc,_i,Pc,_i,[0,Pc,Mc],_i,Cc,_i,[0,Cc,Mc],_i,Dc,_i,[0,ni,-3,di,Ti],_i,[0,ni,-3,di],_i,[0,mi,ni,-2,di,hi,di,-1,2,ni,Mc],_i,Oc,_i,[0,Oc,Mc],ni,Mc,mi,_i,[0,ni,-3,di,Mc,-1],_i,[0,yi,Dc]],mi,[0,mi,hi,-1,di]]);var Vc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect_in\",!1),this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new vo,this.s=new go,dn(this.h,0,3,this.s),dn(t=this.h,0,1,e=new Is)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return\"outputCategoryMask\"in t&&(this.outputCategoryMask=t.outputCategoryMask??!1),\"outputConfidenceMasks\"in t&&(this.outputConfidenceMasks=t.outputConfidenceMasks??!0),super.l(t)}segment(t,e,n,r){const i=\"function\"!=typeof n?n:{};this.j=\"function\"==typeof n?n:r,this.qualityScores=this.categoryMask=this.confidenceMasks=void 0,n=this.B+1,r=new jc;const s=new Bc;var o=new Ic;if(Tn(o,1,255),dn(s,0,12,o),e.keypoint&&e.scribble)throw Error(\"Cannot provide both keypoint and scribble.\");if(e.keypoint){var a=new Uc;wn(a,3,!0),An(a,1,e.keypoint.x),An(a,2,e.keypoint.y),fn(s,5,Gc,a)}else{if(!e.scribble)throw Error(\"Must provide either a keypoint or a scribble.\");for(a of(o=new Nc,e.scribble))wn(e=new Uc,3,!0),An(e,1,a.x),An(e,2,a.y),yn(o,1,Uc,e);fn(s,15,Gc,o)}yn(r,1,Bc,s),this.g.addProtoToStream(r.g(),\"drishti.RenderData\",\"roi_in\",n),Ya(this,t,i);t:{try{const t=new Fc(this.confidenceMasks,this.categoryMask,this.qualityScores);if(!this.j){var c=t;break t}this.j(t)}finally{Jo(this)}c=void 0}return c}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"roi_in\"),Ji(t,\"norm_rect_in\");const e=new Gi;Yn(e,Eo,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.interactive_segmenter.InteractiveSegmenterGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"ROI:roi_in\"),Hi(n,\"NORM_RECT:norm_rect_in\"),n.o(e),qi(t,n),$o(this,t),this.outputConfidenceMasks&&(Zi(t,\"confidence_masks\"),Wi(n,\"CONFIDENCE_MASKS:confidence_masks\"),qo(this,\"confidence_masks\"),this.g.ba(\"confidence_masks\",((t,e)=>{this.confidenceMasks=t.map((t=>qa(this,t,!0,!this.j))),Yo(this,e)})),this.g.attachEmptyPacketListener(\"confidence_masks\",(t=>{this.confidenceMasks=[],Yo(this,t)}))),this.outputCategoryMask&&(Zi(t,\"category_mask\"),Wi(n,\"CATEGORY_MASK:category_mask\"),qo(this,\"category_mask\"),this.g.V(\"category_mask\",((t,e)=>{this.categoryMask=qa(this,t,!1,!this.j),Yo(this,e)})),this.g.attachEmptyPacketListener(\"category_mask\",(t=>{this.categoryMask=void 0,Yo(this,t)}))),Zi(t,\"quality_scores\"),Wi(n,\"QUALITY_SCORES:quality_scores\"),this.g.attachFloatVectorListener(\"quality_scores\",((t,e)=>{this.qualityScores=t,Yo(this,e)})),this.g.attachEmptyPacketListener(\"quality_scores\",(t=>{this.categoryMask=void 0,Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Vc.prototype.segment=Vc.prototype.segment,Vc.prototype.setOptions=Vc.prototype.o,Vc.createFromModelPath=function(t,e){return za(Vc,t,{baseOptions:{modelAssetPath:e}})},Vc.createFromModelBuffer=function(t,e){return za(Vc,t,{baseOptions:{modelAssetBuffer:e}})},Vc.createFromOptions=function(t,e){return za(Vc,t,e)};var Xc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"input_frame_gpu\",\"norm_rect\",!1),this.j={detections:[]},dn(t=this.h=new wo,0,1,e=new Is)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return void 0!==t.displayNamesLocale?He(this.h,2,de(t.displayNamesLocale)):\"displayNamesLocale\"in t&&He(this.h,2),void 0!==t.maxResults?Tn(this.h,3,t.maxResults):\"maxResults\"in t&&He(this.h,3),void 0!==t.scoreThreshold?An(this.h,4,t.scoreThreshold):\"scoreThreshold\"in t&&He(this.h,4),void 0!==t.categoryAllowlist?bn(this.h,5,t.categoryAllowlist):\"categoryAllowlist\"in t&&He(this.h,5),void 0!==t.categoryDenylist?bn(this.h,6,t.categoryDenylist):\"categoryDenylist\"in t&&He(this.h,6),this.l(t)}D(t,e){return this.j={detections:[]},Ya(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},$a(this,t,n,e),this.j}m(){var t=new Qi;Ji(t,\"input_frame_gpu\"),Ji(t,\"norm_rect\"),Zi(t,\"detections\");const e=new Gi;Yn(e,To,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.ObjectDetectorGraph\"),Hi(n,\"IMAGE:input_frame_gpu\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"DETECTIONS:detections\"),n.o(e),qi(t,n),this.g.attachProtoVectorListener(\"detections\",((t,e)=>{for(const e of t)t=hs(e),this.j.detections.push(xo(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"detections\",(t=>{Yo(this,t)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Xc.prototype.detectForVideo=Xc.prototype.F,Xc.prototype.detect=Xc.prototype.D,Xc.prototype.setOptions=Xc.prototype.o,Xc.createFromModelPath=async function(t,e){return za(Xc,t,{baseOptions:{modelAssetPath:e}})},Xc.createFromModelBuffer=function(t,e){return za(Xc,t,{baseOptions:{modelAssetBuffer:e}})},Xc.createFromOptions=function(t,e){return za(Xc,t,e)};var Hc=class{constructor(t,e,n){this.landmarks=t,this.worldLandmarks=e,this.segmentationMasks=n}close(){this.segmentationMasks?.forEach((t=>{t.close()}))}};function Wc(t){t.landmarks=[],t.worldLandmarks=[],t.segmentationMasks=void 0}function zc(t){try{const e=new Hc(t.landmarks,t.worldLandmarks,t.segmentationMasks);if(!t.s)return e;t.s(e)}finally{Jo(t)}}Hc.prototype.close=Hc.prototype.close;var Kc=class extends Ja{constructor(t,e){super(new Wa(t,e),\"image_in\",\"norm_rect\",!1),this.landmarks=[],this.worldLandmarks=[],this.outputSegmentationMasks=!1,dn(t=this.h=new Ao,0,1,e=new Is),this.v=new oo,dn(this.h,0,3,this.v),this.j=new so,dn(this.h,0,2,this.j),Tn(this.j,4,1),An(this.j,2,.5),An(this.v,2,.5),An(this.h,4,.5)}get baseOptions(){return hn(this.h,Is,1)}set baseOptions(t){dn(this.h,0,1,t)}o(t){return\"numPoses\"in t&&Tn(this.j,4,t.numPoses??1),\"minPoseDetectionConfidence\"in t&&An(this.j,2,t.minPoseDetectionConfidence??.5),\"minTrackingConfidence\"in t&&An(this.h,4,t.minTrackingConfidence??.5),\"minPosePresenceConfidence\"in t&&An(this.v,2,t.minPosePresenceConfidence??.5),\"outputSegmentationMasks\"in t&&(this.outputSegmentationMasks=t.outputSegmentationMasks??!1),this.l(t)}D(t,e,n){const r=\"function\"!=typeof e?e:{};return this.s=\"function\"==typeof e?e:n,Wc(this),Ya(this,t,r),zc(this)}F(t,e,n,r){const i=\"function\"!=typeof n?n:{};return this.s=\"function\"==typeof n?n:r,Wc(this),$a(this,t,i,e),zc(this)}m(){var t=new Qi;Ji(t,\"image_in\"),Ji(t,\"norm_rect\"),Zi(t,\"normalized_landmarks\"),Zi(t,\"world_landmarks\"),Zi(t,\"segmentation_masks\");const e=new Gi;Yn(e,bo,this.h);const n=new zi;Xi(n,\"mediapipe.tasks.vision.pose_landmarker.PoseLandmarkerGraph\"),Hi(n,\"IMAGE:image_in\"),Hi(n,\"NORM_RECT:norm_rect\"),Wi(n,\"NORM_LANDMARKS:normalized_landmarks\"),Wi(n,\"WORLD_LANDMARKS:world_landmarks\"),n.o(e),qi(t,n),$o(this,t),this.g.attachProtoVectorListener(\"normalized_landmarks\",((t,e)=>{this.landmarks=[];for(const e of t)t=fs(e),this.landmarks.push(Lo(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"normalized_landmarks\",(t=>{this.landmarks=[],Yo(this,t)})),this.g.attachProtoVectorListener(\"world_landmarks\",((t,e)=>{this.worldLandmarks=[];for(const e of t)t=ls(e),this.worldLandmarks.push(Ro(t));Yo(this,e)})),this.g.attachEmptyPacketListener(\"world_landmarks\",(t=>{this.worldLandmarks=[],Yo(this,t)})),this.outputSegmentationMasks&&(Wi(n,\"SEGMENTATION_MASK:segmentation_masks\"),qo(this,\"segmentation_masks\"),this.g.ba(\"segmentation_masks\",((t,e)=>{this.segmentationMasks=t.map((t=>qa(this,t,!0,!this.s))),Yo(this,e)})),this.g.attachEmptyPacketListener(\"segmentation_masks\",(t=>{this.segmentationMasks=[],Yo(this,t)}))),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Kc.prototype.detectForVideo=Kc.prototype.F,Kc.prototype.detect=Kc.prototype.D,Kc.prototype.setOptions=Kc.prototype.o,Kc.createFromModelPath=function(t,e){return za(Kc,t,{baseOptions:{modelAssetPath:e}})},Kc.createFromModelBuffer=function(t,e){return za(Kc,t,{baseOptions:{modelAssetBuffer:e}})},Kc.createFromOptions=function(t,e){return za(Kc,t,e)},Kc.POSE_CONNECTIONS=vc;export{Ia as DrawingUtils,Za as FaceDetector,uc as FaceLandmarker,lc as FaceStylizer,Uo as FilesetResolver,mc as GestureRecognizer,_c as HandLandmarker,Ac as HolisticLandmarker,bc as ImageClassifier,kc as ImageEmbedder,Rc as ImageSegmenter,Sc as ImageSegmenterResult,Vc as InteractiveSegmenter,Fc as InteractiveSegmenterResult,Ga as MPImage,Ea as MPMask,Xc as ObjectDetector,Kc as PoseLandmarker,Zo as TaskRunner,Ja as VisionTaskRunner};\n//# sourceMappingURL=vision_bundle_mjs.js.map\n"], "mappings": ";AAAA,IAAI,IAAE,eAAa,OAAO,OAAK,OAAK,CAAC;AAAE,SAAS,IAAG;AAAC,QAAM,MAAM,cAAc;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOA,KAAE,OAAO,aAAa,MAAM,MAAKA,EAAC,GAAE,QAAMD,KAAEC,KAAED,KAAEC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,IAAI;AAAE,IAAM,IAAE,eAAa,OAAO;AAAY,SAAS,EAAED,IAAE;AAAC,MAAG,EAAE,CAAAA,MAAG,MAAI,IAAI,eAAa,OAAOA,EAAC;AAAA,OAAM;AAAC,QAAIE,KAAE;AAAE,UAAMC,KAAE,IAAI,WAAW,IAAEH,GAAE,MAAM;AAAE,aAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,UAAIH,KAAED,GAAE,WAAWI,EAAC;AAAE,UAAGH,KAAE,IAAI,CAAAE,GAAED,IAAG,IAAED;AAAA,WAAM;AAAC,YAAGA,KAAE,KAAK,CAAAE,GAAED,IAAG,IAAED,MAAG,IAAE;AAAA,aAAQ;AAAC,cAAGA,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAGA,MAAG,SAAOG,KAAEJ,GAAE,QAAO;AAAC,oBAAMK,KAAEL,GAAE,WAAW,EAAEI,EAAC;AAAE,kBAAGC,MAAG,SAAOA,MAAG,OAAM;AAAC,gBAAAJ,KAAE,QAAMA,KAAE,SAAOI,KAAE,QAAM,OAAMF,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG,KAAIE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAI;AAAA,cAAQ;AAAC,cAAAG;AAAA,YAAG;AAAC,YAAAH,KAAE;AAAA,UAAK;AAAC,UAAAE,GAAED,IAAG,IAAED,MAAG,KAAG,KAAIE,GAAED,IAAG,IAAED,MAAG,IAAE,KAAG;AAAA,QAAG;AAAC,QAAAE,GAAED,IAAG,IAAE,KAAGD,KAAE;AAAA,MAAG;AAAA,IAAC;AAAC,IAAAD,KAAEE,OAAIC,GAAE,SAAOA,KAAEA,GAAE,SAAS,GAAED,EAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAE,GAAE;AAAC,OAAQ,IAAE,CAAC,eAAe,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,KAAG,SAAO,IAAE,EAAE,EAAE,CAAC,CAAC,IAAG;AAAC,QAAE;AAAK,UAAM;AAAA,EAAC;AAAC,MAAE;AAAC;AAAnF;AAAoB;AAAI;AAA4D,IAAI;AAAJ,IAAM,IAAE,KAAG,EAAE,SAAS;AAAE,IAAE,QAAM,KAAG;AAAE,IAAM,IAAE,EAAE;AAAU,SAAS,EAAEA,IAAE;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,MAAM,CAAC,EAAC,OAAMC,GAAC,MAAIA,MAAG,MAAIA,GAAE,QAAQD,EAAC,EAAE;AAAE;AAAC,SAAS,EAAEC,IAAE;AAAC,MAAIC;AAAE,UAAOA,KAAE,EAAE,eAAaA,KAAEA,GAAE,eAAaA,KAAE,KAAI,MAAIA,GAAE,QAAQD,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAM,CAAC,CAAC,MAAI,CAAC,CAAC,KAAG,EAAE,OAAO,SAAO;AAAE;AAAC,SAAS,IAAG;AAAC,SAAO,EAAE,IAAE,EAAE,UAAU,KAAG,EAAE,QAAQ,KAAG,EAAE,OAAO,MAAI,EAAE,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,MAAM;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAE,GAAG,EAAEA,EAAC,GAAEA;AAAC;AAAC,IAAE,KAAG,EAAE,iBAAe,MAAK,EAAE,GAAG,IAAE,WAAU;AAAC;AAAE,IAAI,IAAE,CAAC,EAAE,MAAI,EAAE,SAAS,KAAG,EAAE,MAAM;AAAG,CAAC,EAAE,SAAS,KAAG,EAAE,GAAE,EAAE,GAAE,EAAE,QAAQ,MAAI,EAAE,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,OAAO,KAAG,CAAC,EAAE,KAAG,EAAE,MAAM,MAAI,EAAE,IAAE,EAAE,gBAAgB,IAAE,EAAE,MAAM,MAAI,EAAE,KAAG,EAAE,OAAO;AAAG,IAAI,IAAE,CAAC;AAAP,IAAS,IAAE;AAAK,SAAS,EAAEA,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAO,MAAIE,KAAE,IAAED,KAAE;AAAE,EAAAC,KAAE,IAAEA,KAAE,KAAK,MAAMA,EAAC,IAAE,MAAI,KAAK,QAAQF,GAAEC,KAAE,CAAC,CAAC,MAAIC,KAAE,MAAI,KAAK,QAAQF,GAAEC,KAAE,CAAC,CAAC,IAAEC,KAAE,IAAEA,KAAE;AAAG,QAAMC,KAAE,IAAI,WAAWD,EAAC;AAAE,MAAIE,KAAE;AAAE,UAAO,SAASJ,IAAEC,IAAE;AAAC,aAASC,GAAED,IAAE;AAAC,aAAKE,KAAEH,GAAE,UAAQ;AAAC,cAAMC,KAAED,GAAE,OAAOG,IAAG,GAAED,KAAE,EAAED,EAAC;AAAE,YAAG,QAAMC,GAAE,QAAOA;AAAE,YAAG,CAAC,cAAc,KAAKD,EAAC,EAAE,OAAM,MAAM,sCAAoCA,EAAC;AAAA,MAAC;AAAC,aAAOA;AAAA,IAAC;AAAC,MAAE;AAAE,QAAIE,KAAE;AAAE,eAAO;AAAC,YAAMH,KAAEE,GAAE,EAAE,GAAEC,KAAED,GAAE,CAAC,GAAEE,KAAEF,GAAE,EAAE,GAAEG,KAAEH,GAAE,EAAE;AAAE,UAAG,OAAKG,MAAG,OAAKL,GAAE;AAAM,MAAAC,GAAED,MAAG,IAAEG,MAAG,CAAC,GAAE,MAAIC,OAAIH,GAAEE,MAAG,IAAE,MAAIC,MAAG,CAAC,GAAE,MAAIC,MAAGJ,GAAEG,MAAG,IAAE,MAAIC,EAAC;AAAA,IAAE;AAAA,EAAC,GAAEL,KAAG,SAASA,IAAE;AAAC,IAAAG,GAAEC,IAAG,IAAEJ;AAAA,EAAC,EAAE,GAAEI,OAAIF,KAAEC,GAAE,SAAS,GAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,IAAG;AAAC,MAAG,CAAC,GAAE;AAAC,QAAE,CAAC;AAAE,QAAIH,KAAE,iEAAiE,MAAM,EAAE,GAAEC,KAAE,CAAC,OAAM,MAAK,OAAM,OAAM,IAAI;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEH,GAAE,OAAOC,GAAEC,EAAC,EAAE,MAAM,EAAE,CAAC;AAAE,QAAEA,EAAC,IAAEC;AAAE,eAAQH,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,cAAMC,KAAEE,GAAEH,EAAC;AAAE,mBAAS,EAAEC,EAAC,MAAI,EAAEA,EAAC,IAAED;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAI,IAAE,eAAa,OAAO;AAA1B,IAAqC,IAAE,CAAC,KAAG,cAAY,OAAO;AAAK,SAAS,EAAEA,IAAE;AAAC,MAAG,CAAC,GAAE;AAAC,QAAIC;AAAE,eAASA,OAAIA,KAAE,IAAG,EAAE,GAAEA,KAAE,EAAEA,EAAC;AAAE,QAAIC,KAAE,MAAM,KAAK,MAAMF,GAAE,SAAO,CAAC,CAAC,GAAEG,KAAEF,GAAE,EAAE,KAAG;AAAG,QAAIK,KAAE,GAAEC,KAAE;AAAE,WAAKD,KAAEN,GAAE,SAAO,GAAEM,MAAG,GAAE;AAAC,UAAIF,KAAEJ,GAAEM,EAAC,GAAED,KAAEL,GAAEM,KAAE,CAAC,GAAEE,KAAER,GAAEM,KAAE,CAAC,GAAEG,KAAER,GAAEG,MAAG,CAAC;AAAE,MAAAA,KAAEH,IAAG,IAAEG,OAAI,IAAEC,MAAG,CAAC,GAAEA,KAAEJ,IAAG,KAAGI,OAAI,IAAEG,MAAG,CAAC,GAAEA,KAAEP,GAAE,KAAGO,EAAC,GAAEN,GAAEK,IAAG,IAAEE,KAAEL,KAAEC,KAAEG;AAAA,IAAC;AAAC,YAAOC,KAAE,GAAED,KAAEL,IAAEH,GAAE,SAAOM,IAAE;AAAA,MAAC,KAAK;AAAE,QAAAE,KAAEP,IAAG,MAAIQ,KAAET,GAAEM,KAAE,CAAC,OAAK,CAAC,KAAGH;AAAA,MAAE,KAAK;AAAE,QAAAH,KAAEA,GAAEM,EAAC,GAAEJ,GAAEK,EAAC,IAAEN,GAAED,MAAG,CAAC,IAAEC,IAAG,IAAED,OAAI,IAAES,MAAG,CAAC,IAAED,KAAEL;AAAA,IAAC;AAAC,WAAOD,GAAE,KAAK,EAAE;AAAA,EAAC;AAAC,OAAID,KAAE,IAAGC,KAAE,GAAEC,KAAEH,GAAE,SAAO,OAAME,KAAEC,KAAG,CAAAF,MAAG,OAAO,aAAa,MAAM,MAAKD,GAAE,SAASE,IAAEA,MAAG,KAAK,CAAC;AAAE,SAAOD,MAAG,OAAO,aAAa,MAAM,MAAKC,KAAEF,GAAE,SAASE,EAAC,IAAEF,EAAC,GAAE,KAAKC,EAAC;AAAC;AAAC,IAAM,IAAE;AAAR,IAAiB,IAAE,EAAC,KAAI,KAAI,GAAE,KAAI,KAAI,IAAG;AAAE,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG;AAAE;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAG,CAAC,EAAE,QAAO,EAAEA,EAAC;AAAE,IAAE,KAAKA,EAAC,MAAIA,KAAEA,GAAE,QAAQ,GAAE,CAAC,IAAGA,KAAE,KAAKA,EAAC;AAAE,QAAMC,KAAE,IAAI,WAAWD,GAAE,MAAM;AAAE,WAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,GAAEC,EAAC,IAAEF,GAAE,WAAWE,EAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,KAAG,QAAMA,MAAGA,cAAa;AAAU;AAAC,IAAI,IAAE,CAAC;AAAE,SAAS,IAAG;AAAC,SAAO,MAAI,IAAI,EAAE,MAAK,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,IAAE,CAAC;AAAE,MAAIC,KAAED,GAAE;AAAE,SAAO,SAAOC,KAAE,QAAMA,MAAG,EAAEA,EAAC,IAAEA,KAAE,YAAU,OAAOA,KAAE,EAAEA,EAAC,IAAE,QAAMA,KAAED,GAAE,IAAEC;AAAC;AAAC,IAAI,IAAE,MAAK;AAAA,EAAC,IAAG;AAAC,WAAO,IAAI,WAAW,EAAE,IAAI,KAAG,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE,KAAK,IAAED,IAAE,QAAMA,MAAG,MAAIA,GAAE,OAAO,OAAM,MAAM,wDAAwD;AAAA,EAAC;AAAC;AAAE,IAAI;AAAJ,IAAM;AAAE,SAAS,EAAEA,IAAE;AAAC,MAAGA,OAAI,EAAE,OAAM,MAAM,yBAAyB;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,EAAAD,GAAE,sCAAoCA,GAAE,oCAAkC,CAAC,IAAGA,GAAE,kCAAkC,WAASC;AAAC;AAAC,SAAS,EAAED,IAAE;AAAC,SAAO,EAAEA,KAAE,MAAMA,EAAC,GAAE,SAAS,GAAEA;AAAC;AAAC,SAAS,EAAEC,IAAE;AAAC,MAAG,QAAMA,IAAE;AAAC,QAAIC,KAAE,MAAI,CAAC,GAAEC,KAAED,GAAED,EAAC,KAAG;AAAE,IAAAE,MAAG,MAAID,GAAED,EAAC,IAAEE,KAAE,GAAE,EAAEF,KAAE,MAAM,GAAE,UAAU,IAAE,SAASA,IAAE;AAAC,QAAE,YAAY,MAAI;AAAC,cAAMA;AAAA,MAAC,IAAG,CAAC;AAAA,IAAC,GAAEA,EAAC;AAAA,EAAE;AAAC;AAAC,IAAI,IAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO;AAAE,SAAS,EAAED,IAAEC,IAAEC,KAAE,OAAG;AAAC,SAAM,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO,IAAEA,MAAG,OAAO,OAAKF,KAAE,OAAO,IAAIA,EAAC,IAAE,QAAMA,KAAE,OAAOA,EAAC,IAAE,OAAO,IAAEC;AAAC;AAAC,IAAI,IAAE,EAAE,OAAM,QAAO,IAAE;AAAvB,IAAyB,IAAE,EAAE,QAAO,KAAK;AAAzC,IAA2C,IAAE,EAAE,QAAO,KAAK;AAA3D,IAA6D,IAAE,EAAE,QAAO,OAAO,CAAC;AAAhF,IAAkF,IAAE,EAAE,QAAO,OAAO;AAApG,IAAsG,IAAE,EAAE,QAAO,MAAM;AAAE,IAAM,IAAE,IAAE,IAAE;AAAZ,IAAiB,KAAG,EAAC,IAAG,EAAC,OAAM,GAAE,cAAa,MAAG,UAAS,MAAG,YAAW,MAAE,EAAC;AAA3E,IAA6E,KAAG,OAAO;AAAiB,SAAS,GAAGD,IAAEC,IAAE;AAAC,OAAG,KAAKD,MAAG,GAAGA,IAAE,EAAE,GAAEA,GAAE,CAAC,KAAGC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,OAAG,KAAKD,MAAG,GAAGA,IAAE,EAAE,GAAEA,GAAE,CAAC,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,GAAGA,IAAE,EAAE,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGA,IAAE,UAAQ,IAAED,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGA,IAAE,UAAQ,KAAGD,GAAE;AAAC;AAAC,SAAS,KAAI;AAAC,SAAM,cAAY,OAAO;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAM,UAAU,MAAM,KAAKA,EAAC;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAO,SAAOA,MAAG,YAAU,OAAOA,MAAG,CAAC,MAAM,QAAQA,EAAC,KAAGA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAG,QAAMD;AAAE,QAAG,YAAU,OAAOA,GAAE,CAAAA,KAAEA,KAAE,IAAI,EAAEA,IAAE,CAAC,IAAE,EAAE;AAAA,aAAUA,GAAE,gBAAc,EAAE,KAAG,EAAEA,EAAC,EAAE,CAAAA,KAAEA,GAAE,SAAO,IAAI,EAAE,IAAI,WAAWA,EAAC,GAAE,CAAC,IAAE,EAAE;AAAA,SAAM;AAAC,UAAG,CAACC,GAAE,OAAM,MAAM;AAAE,MAAAD,KAAE;AAAA,IAAM;AAAA;AAAC,SAAOA;AAAC;AAAC,IAAM,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA,GAAE,OAAM,MAAM;AAAC;AAAC,GAAG,IAAG,EAAE,GAAE,KAAG,OAAO,OAAO,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEF,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,UAAMF,KAAE,KAAK,EAAE,KAAK;AAAE,WAAOA,GAAE,SAAOA,GAAE,QAAM,KAAK,EAAE,KAAK,KAAK,GAAEA,GAAE,KAAK,IAAGA;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO;AAAA,EAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,IAAEA,GAAE,CAAC,IAAE;AAAM;AAAC,IAAI,KAAG,OAAO,OAAO,CAAC,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,KAAG,MAAGA;AAAC;AAAC,IAAI,KAAG,IAAI,CAAAA,OAAG,YAAU,OAAOA,GAAE;AAAjC,IAAmC,KAAG,IAAI,CAAAA,OAAG,YAAU,OAAOA,GAAE;AAAhE,IAAkE,KAAG,IAAI,CAAAA,OAAG,aAAW,OAAOA,GAAE;AAAhG,IAAkG,KAAG,cAAY,OAAO,EAAE,UAAQ,YAAU,OAAO,EAAE,OAAO,CAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED;AAAE,MAAG,GAAGC,EAAC,GAAE;AAAC,QAAG,CAAC,4BAA4B,KAAKA,EAAC,EAAE,OAAM,MAAM,OAAOA,EAAC,CAAC;AAAA,EAAC,WAAS,GAAGA,EAAC,KAAG,CAAC,OAAO,cAAcA,EAAC,EAAE,OAAM,MAAM,OAAOA,EAAC,CAAC;AAAE,SAAO,KAAG,OAAOD,EAAC,IAAEA,KAAE,GAAGA,EAAC,IAAEA,KAAE,MAAI,MAAI,GAAGA,EAAC,IAAEA,GAAE,KAAK,KAAG,MAAI,OAAOA,EAAC;AAAC;AAAC,IAAI,KAAG,IAAI,CAAAA,OAAG,KAAGA,MAAG,MAAIA,MAAG,KAAG,QAAMA,GAAE,CAAC,IAAE,GAAGA,IAAE,EAAE,IAAE,GAAGA,IAAE,EAAE,EAAE;AAAE,IAAM,KAAG,OAAO,iBAAiB,SAAS;AAA1C,IAA4C,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAlF,IAAyF,KAAG,OAAO,iBAAiB,SAAS;AAA7H,IAA+H,KAAG,KAAG,OAAO,OAAO,gBAAgB,IAAE;AAAO,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,SAAOC,GAAE,OAAO,QAAM;AAAG,MAAGD,GAAE,SAAOC,GAAE,UAAQD,OAAIC,GAAE,QAAM;AAAG,WAAQC,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,UAAMC,KAAEH,GAAEE,EAAC,GAAEE,KAAEH,GAAEC,EAAC;AAAE,QAAGC,KAAEC,GAAE,QAAM;AAAG,QAAGD,KAAEC,GAAE,QAAM;AAAA,EAAE;AAAC;AAAC,IAAM,KAAG,cAAY,OAAO,WAAW,UAAU;AAAM,IAAI;AAAJ,IAAO,KAAG;AAAV,IAAY,KAAG;AAAE,SAAS,GAAGJ,IAAE;AAAC,QAAMC,KAAED,OAAI;AAAE,OAAGC,IAAE,MAAID,KAAEC,MAAG,eAAa;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAGA,KAAE,GAAE;AAAC,OAAG,CAACA,EAAC;AAAE,UAAK,CAACC,IAAEC,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,SAAGD,OAAI,GAAE,KAAGC,OAAI;AAAA,EAAC,MAAM,IAAGF,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,OAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;AAAE,EAAAA,GAAE,WAAW,GAAE,CAACD,IAAE,IAAE,GAAE,KAAG,GAAE,KAAGC,GAAE,UAAU,GAAE,IAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAMC,KAAE,aAAWD,MAAGD,OAAI;AAAG,SAAO,OAAO,cAAcE,EAAC,IAAEA,KAAE,GAAGF,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAMC,KAAE,aAAWD;AAAE,SAAOC,OAAID,KAAE,CAACA,OAAI,GAAE,MAAID,KAAE,IAAE,CAACA,OAAI,OAAKC,KAAEA,KAAE,MAAI,KAAI,YAAU,QAAOD,KAAE,GAAGA,IAAEC,EAAC,KAAGC,KAAE,CAACF,KAAEA,KAAEE,KAAE,MAAIF,KAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,QAAK,IAAGC,QAAK,MAAI,QAAQ,KAAIC,KAAE,MAAI,aAAWD,KAAED;AAAA,MAAQ,IAAG,IAAEE,KAAE,MAAI,OAAOD,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOD,EAAC,MAAIA,MAAG,WAASA,MAAG,WAASE,KAAE,YAAUF,OAAI,KAAGC,MAAG,MAAI,WAASA,KAAEA,MAAG,KAAG,QAAOC,MAAG,UAAQD,IAAEA,MAAG,GAAED,MAAG,QAAME,MAAGF,KAAE,QAAM,GAAEA,MAAG,MAAKE,MAAG,QAAMD,MAAGC,KAAE,QAAM,GAAEA,MAAG,MAAKA,KAAED,KAAE,GAAGC,EAAC,IAAE,GAAGF,EAAC;AAAG,SAAOE;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,SAAOA,KAAE,OAAOA,EAAC,GAAE,UAAU,MAAMA,GAAE,MAAM,IAAEA;AAAC;AAAC,SAAS,KAAI;AAAC,MAAIA,KAAE,IAAGC,KAAE;AAAG,MAAG,aAAWA,GAAE,KAAG,GAAG,EAAE,CAAAD,KAAE,MAAI,OAAO,IAAEC,EAAC,KAAG,OAAO,EAAE,IAAE,OAAOD,OAAI,CAAC;AAAA,OAAO;AAAC,UAAK,CAACE,IAAEC,EAAC,IAAE,GAAGH,IAAEC,EAAC;AAAE,IAAAD,KAAE,MAAI,GAAGE,IAAEC,EAAC;AAAA,EAAC;AAAA,MAAM,CAAAH,KAAE,GAAGA,IAAEC,EAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,GAAE,SAAO,GAAG,IAAG,OAAOA,EAAC,CAAC;AAAA,WAAU,GAAG,EAAE,CAAAA,KAAE,OAAOA,EAAC,GAAE,KAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,MAAI,GAAE,KAAG,OAAOA,MAAG,OAAO,EAAE,IAAE,OAAO,UAAU,CAAC;AAAA,OAAM;AAAC,UAAMC,KAAE,EAAE,QAAMD,GAAE,CAAC;AAAG,SAAG,KAAG;AAAE,UAAME,KAAEF,GAAE;AAAO,aAAQG,KAAEF,IAAEG,MAAGF,KAAED,MAAG,IAAEA,IAAEG,MAAGF,IAAEC,KAAEC,IAAEA,MAAG,GAAE;AAAC,YAAMH,KAAE,OAAOD,GAAE,MAAMG,IAAEC,EAAC,CAAC;AAAE,YAAI,KAAI,KAAG,MAAI,KAAGH,IAAE,MAAI,eAAa,MAAI,KAAK,MAAM,KAAG,UAAU,GAAE,QAAM,GAAE,QAAM;AAAA,IAAE;AAAC,QAAGA,IAAE;AAAC,YAAK,CAACD,IAAEC,EAAC,IAAE,GAAG,IAAG,EAAE;AAAE,WAAGD,IAAE,KAAGC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAOA,KAAE,CAACA,IAAED,KAAEA,KAAE,IAAE,CAACA,KAAEC,MAAG,GAAE,CAACD,IAAEC,EAAC;AAAC;AAAC,IAAM,KAAG,cAAY,OAAO,SAAO,OAAO,SAAO;AAAjD,IAAwD,KAAG,cAAY,OAAO,SAAO,OAAO,UAAQ;AAApG,IAA2G,KAAG,OAAO;AAArH,IAAmI,KAAG,OAAO;AAA7I,IAAsJ,KAAG,KAAK;AAA9J,IAAoK,KAAG,GAAG,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE,UAAQA,MAAG,eAAaA,MAAG,gBAAcA,KAAE,OAAOA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,aAAW,OAAOA,KAAEA,KAAE,YAAU,OAAOA,KAAE,CAAC,CAACA,KAAE;AAAM;AAAC,IAAM,KAAG;AAAiC,SAAS,GAAGA,IAAE;AAAC,UAAO,OAAOA,IAAE;AAAA,IAAC,KAAI;AAAS,aAAM;AAAA,IAAG,KAAI;AAAS,aAAO,GAAGA,EAAC;AAAA,IAAE,KAAI;AAAS,aAAO,GAAG,KAAKA,EAAC;AAAA,IAAE;AAAQ,aAAM;AAAA,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,GAAE,QAAOA;AAAE,MAAG,YAAU,OAAOA,MAAGA,GAAE,CAAAA,KAAE,CAACA;AAAA,WAAU,YAAU,OAAOA,GAAE;AAAO,SAAO,GAAGA,EAAC,IAAE,IAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,GAAE,QAAOA;AAAE,MAAG,YAAU,OAAOA,MAAGA,GAAE,CAAAA,KAAE,CAACA;AAAA,WAAU,YAAU,OAAOA,GAAE;AAAO,SAAO,GAAGA,EAAC,IAAEA,OAAI,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,GAAE,CAAC,EAAE,QAAM;AAAG,QAAMC,KAAED,GAAE;AAAO,SAAOC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAO,SAAM,QAAMA,GAAE,CAAC,IAAEC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE,UAAQC,KAAE,MAAI,OAAKA,MAAG,OAAOD,GAAE,UAAU,GAAE,CAAC,CAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,EAAC,IAAEA,MAAG,GAAGA,EAAC,GAAE,GAAG;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,GAAGA,EAAC,GAAE,GAAGA,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAE,GAAG,OAAOD,EAAC,CAAC;AAAE,SAAO,GAAGC,EAAC,IAAE,OAAOA,EAAC,KAAG,QAAMA,KAAED,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEC,EAAC,IAAG,GAAGD,EAAC;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAE,GAAG,OAAOD,EAAC,CAAC;AAAE,SAAO,GAAGC,EAAC,IAAE,GAAGA,EAAC,KAAG,QAAMA,KAAED,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEC,EAAC,IAAG,GAAG,IAAE,GAAG,GAAG,IAAG,OAAOD,EAAC,CAAC,CAAC,IAAE,GAAG,GAAGA,EAAC,CAAC;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,GAAGA,EAAC,EAAE,CAAAA,KAAE,GAAG,GAAGA,EAAC,CAAC;AAAA,OAAM;AAAC,QAAGA,KAAE,GAAGA,EAAC,GAAE,GAAGA,EAAC,EAAE,CAAAA,KAAE,OAAOA,EAAC;AAAA,SAAM;AAAC,YAAMC,KAAE,OAAOD,EAAC;AAAE,SAAGC,EAAC,IAAED,KAAEC,MAAG,GAAGD,EAAC,GAAEA,KAAE,GAAG;AAAA,IAAE;AAAC,IAAAA,KAAE,GAAGA,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,KAAEA,KAAE,YAAU,OAAOA,MAAG,GAAGA,EAAC,IAAEA,KAAE,OAAOA,EAAC,KAAGA,KAAE,GAAG,IAAGA,EAAC,GAAEA,KAAE,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,OAAOA,EAAC,IAAGA,MAAG,GAAGA,EAAC,IAAE,YAAU,OAAOA,KAAE,GAAGA,EAAC,IAAE,GAAGA,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,GAAE,QAAOA;AAAE,MAAIC,KAAE,OAAOD;AAAE,MAAG,aAAWC,GAAE,QAAO,OAAO,GAAG,IAAGD,EAAC,CAAC;AAAE,MAAG,GAAGA,EAAC,GAAE;AAAC,QAAG,aAAWC,GAAE,QAAOA,KAAE,GAAG,OAAOD,EAAC,CAAC,GAAE,GAAGC,EAAC,KAAGA,MAAG,IAAED,KAAE,OAAOC,EAAC,KAAG,QAAMA,KAAED,GAAE,QAAQ,GAAG,OAAKA,KAAEA,GAAE,UAAU,GAAEC,EAAC,IAAG,GAAGD,EAAC,MAAI,GAAGA,EAAC,GAAEA,KAAE,GAAG,IAAG,EAAE,KAAIA;AAAE,QAAG,aAAWC,GAAE,SAAOD,KAAE,GAAGA,EAAC,MAAI,KAAG,GAAGA,EAAC,IAAEA,MAAE,SAASA,IAAE;AAAC,UAAGA,KAAE,GAAE;AAAC,WAAGA,EAAC;AAAE,YAAIC,KAAE,GAAG,IAAG,EAAE;AAAE,eAAOD,KAAE,OAAOC,EAAC,GAAE,GAAGD,EAAC,IAAEA,KAAEC;AAAA,MAAC;AAAC,aAAO,GAAGA,KAAE,OAAOD,EAAC,CAAC,IAAEC,MAAG,GAAGD,EAAC,GAAE,GAAG,IAAG,EAAE;AAAA,IAAE,GAAEA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA,GAAE,OAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA,GAAE,OAAM,MAAM;AAAE,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,QAAMA,MAAG,YAAU,OAAOA,KAAEA,KAAE;AAAM;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMH,MAAG,YAAU,OAAOA,MAAGA,GAAE,MAAI,GAAG,QAAOA;AAAE,MAAG,CAAC,MAAM,QAAQA,EAAC,EAAE,QAAOE,KAAE,IAAEC,OAAIH,KAAEC,GAAE,CAAC,OAAK,IAAID,KAAE,IAAIC,MAAG,CAAC,GAAED,KAAEC,GAAE,CAAC,IAAED,KAAGC,KAAED,MAAGC,KAAE,IAAIA,OAAEA,KAAE,QAAOA;AAAE,MAAIG,KAAEF,KAAE,IAAEF,GAAE,CAAC;AAAE,SAAO,MAAII,OAAIA,MAAG,KAAGD,KAAGC,MAAG,IAAED,IAAEC,OAAIF,MAAG,GAAGF,IAAEI,EAAC,GAAE,IAAIH,GAAED,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,IAAE;AAAC,QAAG,CAAC,GAAGA,KAAED,EAAC,EAAE,OAAM,EAAE,OAAO;AAAE,YAAO,OAAOC,IAAE;AAAA,MAAC,KAAI;AAAS,QAAAA,KAAE,GAAGA,EAAC;AAAE,cAAM;AAAA,MAAE,KAAI;AAAS,QAAAA,KAAE,GAAG,GAAG,IAAGA,EAAC,CAAC;AAAE,cAAM;AAAA,MAAE;AAAQ,QAAAA,KAAE,GAAGA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,MAAM,CAAAD,KAAE,QAAOC,KAAED,KAAGC,KAAE,QAAMA,KAAEA,KAAE,aAAWD,KAAE,GAAG,GAAG,IAAGC,EAAC,CAAC,IAAE,GAAGA,EAAC,IAAE,aAAWD,KAAE,GAAGC,EAAC,IAAE,GAAGA,EAAC,IAAE;AAAO,SAAO,SAAOD,KAAEC,MAAGC,KAAE,KAAG,SAAOF;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA;AAAC;AAAC,IAAM,KAAG,CAAC;AAAE,IAAI,MAAG,WAAU;AAAC,MAAG;AAAC,WAAO,EAAE,IAAI,cAAc,IAAG;AAAA,MAAC,cAAa;AAAC,cAAM;AAAA,MAAC;AAAA,IAAC,GAAC,GAAE;AAAA,EAAE,QAAM;AAAC,WAAM;AAAA,EAAE;AAAC,GAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,cAAa;AAAC,SAAK,IAAE,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAEC,IAAE;AAAC,WAAO,KAAK,EAAE,IAAID,IAAEC,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAK;AAAA,EAAI;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAOA,KAAE,KAAK,EAAE,OAAOA,EAAC,GAAE,KAAK,OAAK,KAAK,EAAE,MAAKA;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,KAAK,OAAK,KAAK,EAAE;AAAA,EAAI;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,EAAE,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,EAAE,KAAK;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO,KAAK,EAAE,OAAO;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,WAAO,KAAK,EAAE,QAAQD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,MAAI,OAAO,eAAe,GAAG,WAAU,IAAI,SAAS,GAAE,OAAO,iBAAiB,GAAG,WAAU,EAAC,MAAK,EAAC,OAAM,GAAE,cAAa,MAAG,YAAW,MAAG,UAAS,KAAE,EAAC,CAAC,GAAE,MAAI,cAAc,IAAG;AAAA,EAAC,cAAa;AAAC,UAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,IAAEA,GAAE,EAAE,OAAM,MAAM,gCAAgC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAEC,KAAE,IAAGC,KAAE,IAAG;AAAC,UAAM;AAAE,QAAIC,KAAE,IAAEJ,GAAE,CAAC;AAAE,IAAAI,MAAG,IAAG,GAAGJ,IAAEI,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,IAAEH,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAE,KAAK,IAAE,KAAGC;AAAE,aAAQE,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,YAAMG,KAAER,GAAEK,EAAC,GAAEI,KAAEP,GAAEM,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,UAAIF,KAAEE,GAAE,CAAC;AAAE,MAAAP,KAAE,WAASK,OAAIA,KAAE,QAAMA,KAAEH,GAAEK,GAAE,CAAC,GAAE,OAAG,MAAG,QAAO,QAAOJ,EAAC,GAAE,MAAM,IAAIK,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIN,KAAE;AAAG,QAAG,MAAI,KAAK,KAAK,QAAO,MAAM,KAAK,MAAM,QAAQ,IAAG,CAAAC,QAAIA,GAAE,CAAC,IAAED,GAAEC,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAED,GAAEC,GAAE,CAAC,CAAC,GAAEA,IAAG;AAAA,EAAC;AAAA,EAAC,GAAGD,KAAE,IAAG;AAAC,UAAMC,KAAE,CAAC,GAAEC,KAAE,MAAM,QAAQ;AAAE,aAAQC,IAAE,EAAEA,KAAED,GAAE,KAAK,GAAG,OAAM,EAACC,KAAEA,GAAE,OAAO,CAAC,IAAEH,GAAEG,GAAE,CAAC,CAAC,GAAEA,GAAE,CAAC,IAAEH,GAAEG,GAAE,CAAC,CAAC,GAAEF,GAAE,KAAKE,EAAC;AAAE,WAAOF;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,OAAG,IAAI,GAAE,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,MAAM,OAAO,KAAK,EAAEA,IAAE,MAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,GAAE;AAAC,UAAIA,KAAE,MAAM,KAAK;AAAE,MAAAA,KAAE,IAAI,GAAGA,IAAE,IAAG,IAAI;AAAA,IAAC,MAAM,CAAAA,KAAE,MAAM,QAAQ;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAG,KAAK,GAAE;AAAC,UAAIA,KAAE,MAAM,KAAK;AAAE,MAAAA,KAAE,IAAI,GAAGA,IAAE,GAAG,UAAU,KAAI,IAAI;AAAA,IAAC,MAAM,CAAAA,KAAE,MAAM,OAAO;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,SAAK,IAAE,MAAM,SAAS,CAACC,IAAEC,IAAEC,OAAI;AAAC,MAAAJ,GAAE,KAAKC,IAAEG,GAAE,IAAID,EAAC,GAAEA,IAAEC,EAAC;AAAA,IAAC,EAAE,IAAE,MAAM,QAAQJ,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAID,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,SAAOD,KAAE,KAAK,EAAEA,IAAE,MAAG,KAAE,KAAG,OAAK,QAAMC,MAAG,MAAM,OAAOD,EAAC,GAAE,QAAM,MAAM,IAAIA,IAAE,KAAK,EAAEC,IAAE,MAAG,MAAG,KAAK,GAAE,OAAG,KAAK,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAE;AAAC,UAAMC,KAAE,KAAK,EAAED,GAAE,CAAC,GAAE,OAAG,IAAE;AAAE,IAAAA,KAAEA,GAAE,CAAC,GAAEA,KAAE,KAAK,IAAE,WAASA,KAAE,OAAKA,KAAE,KAAK,EAAEA,IAAE,OAAG,MAAG,QAAO,OAAG,KAAK,CAAC,GAAE,MAAM,IAAIC,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,MAAM,IAAI,KAAK,EAAEA,IAAE,OAAG,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,IAAAA,KAAE,KAAK,EAAEA,IAAE,OAAG,KAAE;AAAE,UAAMC,KAAE,MAAM,IAAID,EAAC;AAAE,QAAG,WAASC,IAAE;AAAC,UAAIC,KAAE,KAAK;AAAE,aAAOA,OAAIA,KAAE,KAAK,EAAED,IAAE,OAAG,MAAGC,IAAE,KAAK,IAAG,KAAK,CAAC,OAAKD,MAAG,MAAM,IAAID,IAAEE,EAAC,GAAEA,MAAGD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,CAAC,OAAO,QAAQ,IAAG;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOL,KAAE,GAAGA,IAAEG,IAAED,IAAEG,EAAC,GAAED,OAAIJ,KAAE,GAAGA,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAACA,IAAE,KAAK,IAAIA,EAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAO;AAAP,IAAU;AAAV,IAAa;AAAG,SAAS,KAAI;AAAC,SAAO,OAAK,IAAI,GAAG,GAAG,CAAC,CAAC,GAAE,QAAO,QAAO,QAAO,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMJ,IAAE;AAAC,QAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,YAAMK,KAAE,IAAEL,GAAE,CAAC;AAAE,aAAO,MAAIA,GAAE,UAAQ,IAAEK,KAAE,SAAOD,MAAG,IAAEC,KAAEL,KAAE,GAAGA,IAAEC,IAAEC,IAAE,WAASC,IAAEC,EAAC;AAAA,IAAC;AAAC,WAAOH,GAAED,IAAEG,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,MAAGD,KAAE,IAAEF,GAAE,CAAC,IAAE,GAAEQ,KAAEL,KAAE,CAAC,EAAE,KAAGE,MAAG;AAAO,MAAII,KAAE;AAAE,QAAMH,MAAGH,KAAE,GAAGH,EAAC,GAAG;AAAO,WAAQA,KAAE,GAAEA,KAAEM,IAAEN,MAAI;AAAC,QAAIO,KAAEJ,GAAEH,EAAC;AAAE,QAAGA,OAAIM,KAAE,KAAG,GAAGC,EAAC,GAAE;AAAC,UAAIG,KAAET,IAAE,IAAEC,IAAE,IAAEM,IAAE,IAAEJ;AAAE,UAAIJ;AAAE,eAAQC,MAAKM,IAAE;AAAC,cAAML,KAAE,GAAGK,GAAEN,EAAC,GAAES,IAAE,GAAE,GAAE,CAAC;AAAE,gBAAMR,QAAKF,OAAI,CAAC,GAAGC,EAAC,IAAEC;AAAA,MAAE;AAAC,MAAAK,KAAEP;AAAA,IAAC,MAAM,CAAAO,KAAE,GAAGJ,GAAEH,EAAC,GAAEC,IAAEC,IAAEM,IAAEJ,EAAC;AAAE,IAAAD,GAAEH,EAAC,IAAEO,IAAE,QAAMA,OAAIE,KAAET,KAAE;AAAA,EAAE;AAAC,SAAOS,KAAEH,OAAIH,GAAE,SAAOM,KAAGP,QAAKF,KAAE,GAAGA,EAAC,OAAKG,GAAE,CAAC,IAAE,GAAGH,EAAC,IAAGE,GAAEG,IAAEF,EAAC,IAAGA;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,SAAO,GAAGA,IAAE,IAAG,QAAO,QAAO,KAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,UAAO,OAAOA,IAAE;AAAA,IAAC,KAAI;AAAS,aAAO,OAAO,SAASA,EAAC,IAAEA,KAAE,KAAGA;AAAA,IAAE,KAAI;AAAS,aAAO,GAAGA,EAAC,IAAE,OAAOA,EAAC,IAAE,KAAGA;AAAA,IAAE,KAAI;AAAU,aAAOA,KAAE,IAAE;AAAA,IAAE,KAAI;AAAS,UAAG,EAAEA,EAAC,EAAE,QAAO,EAAEA,EAAC,KAAG,EAAE,CAAC,GAAE,EAAEA,EAAC;AAAE,UAAGA,GAAE,MAAI,GAAG,QAAO,GAAGA,EAAC;AAAE,UAAGA,cAAa,GAAE;AAAC,cAAMC,KAAED,GAAE;AAAE,eAAO,QAAMC,KAAE,KAAG,YAAU,OAAOA,KAAEA,KAAED,GAAE,IAAE,EAAEC,EAAC;AAAA,MAAC;AAAC,aAAOD,cAAa,KAAGA,GAAE,GAAG,IAAE;AAAA,EAAM;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE,EAAAA,KAAE,GAAGC,IAAE,IAAG,QAAO,QAAO,KAAE;AAAE,MAAIC,KAAE,IAAED,GAAE,CAAC;AAAE,OAAIA,KAAED,GAAE,WAAS,EAAE,MAAIE,KAAG;AAAC,QAAIC,KAAEH,GAAEC,KAAE,CAAC,GAAEG,KAAE;AAAG,OAAGD,EAAC,KAAGF,MAAIG,KAAE,QAAID,KAAE;AAAO,QAAIE,KAAEJ,MAAGC,KAAE,MAAIA,KAAE,IAAE,KAAIM,MAAG,MAAI,IAAIH,IAAEH,IAAEF,IAAEG,EAAC;AAAE,QAAGA,OAAIH,GAAEC,EAAC,IAAE,SAAQI,KAAEG,MAAGL,IAAE;AAAC,eAAQM,MAAKJ,KAAE,MAAGF,IAAE;AAAC,cAAMG,KAAE,CAACG;AAAE,QAAAH,MAAGE,MAAGR,GAAEI,KAAEE,KAAEJ,EAAC,IAAEC,GAAEM,EAAC,GAAER,KAAE,KAAK,IAAIG,KAAE,GAAEH,EAAC,GAAEG,KAAE,OAAG,OAAOD,GAAEM,EAAC,KAAGJ,KAAE;AAAA,MAAE;AAAC,MAAAA,OAAIF,KAAE;AAAA,IAAO;AAAC,SAAIE,KAAEJ,KAAE,GAAEA,KAAE,GAAEI,KAAEJ,KAAE,EAAE,KAAG,SAAOQ,KAAET,GAAEK,EAAC,GAAG,CAAAJ,MAAIG,KAAE;AAAA,SAAO;AAAC,UAAG,GAAGC,MAAGH,OAAIM,IAAG;AAAM,OAACL,OAAI,CAAC,GAAGE,EAAC,IAAEI,IAAER,MAAIG,KAAE;AAAA,IAAE;AAAC,IAAAA,OAAIJ,GAAE,SAAOC,KAAGE,MAAGH,GAAE,KAAKG,EAAC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAOF,KAAE,GAAGA,IAAEC,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEC,KAAE,IAAE,CAAC,GAAED,OAAI,MAAIC,MAAG,GAAGF,IAAE,IAAI,GAAEA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMH,IAAE;AAAC,QAAII,KAAE;AAAG,IAAAF,MAAGF,KAAE,CAACE,EAAC,GAAEE,MAAG,OAAKJ,KAAE,CAAC,GAAEC,OAAIG,KAAE,YAAUA,MAAG,OAAKH,OAAI;AAAA,EAAG,OAAK;AAAC,QAAG,CAAC,MAAM,QAAQD,EAAC,EAAE,OAAM,MAAM,MAAM;AAAE,QAAG,QAAMI,KAAE,IAAEJ,GAAE,CAAC,MAAI,EAAE,KAAGI,OAAI,IAAEA,MAAG,EAAE,CAAC,GAAE,OAAKA,GAAE,OAAM,MAAM,MAAM;AAAE,QAAG,KAAGA,GAAE,QAAOJ;AAAE,QAAG,MAAIG,MAAG,MAAIA,OAAIC,MAAG,KAAIF,OAAIE,MAAG,KAAIF,OAAIF,GAAE,CAAC,GAAG,OAAM,MAAM,KAAK;AAAE,OAAE;AAAC,UAAIK,MAAGH,KAAEF,IAAG;AAAO,UAAGK,IAAE;AAAC,YAAIG,KAAEH,KAAE;AAAE,YAAG,GAAGF,KAAED,GAAEM,EAAC,CAAC,GAAE;AAAC,eAAIA,MAAGP,KAAE,OAAKG,MAAG,OAAK,IAAE,OAAK,KAAK,OAAM,MAAM,QAAQ;AAAE,mBAAQK,MAAKN,GAAE,EAACE,KAAE,CAACI,MAAGD,OAAIN,GAAEG,KAAEJ,EAAC,IAAEE,GAAEM,EAAC,GAAE,OAAON,GAAEM,EAAC;AAAG,UAAAL,KAAE,YAAUA,MAAG,OAAKI,OAAI;AAAG,gBAAM;AAAA,QAAC;AAAA,MAAC;AAAC,UAAGP,IAAE;AAAC,aAAIQ,KAAE,KAAK,IAAIR,IAAEI,MAAG,MAAID,KAAE,IAAE,GAAG,KAAG,KAAK,OAAM,MAAM,MAAM;AAAE,QAAAA,KAAE,YAAUA,MAAG,OAAKK,OAAI;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAC,SAAO,GAAGT,IAAEI,EAAC,GAAEJ;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,KAAE,IAAG;AAAC,MAAG,QAAMF,IAAE;AAAC,QAAG,KAAGA,cAAa,WAAW,QAAOC,KAAED,KAAE,IAAI,WAAWA,EAAC;AAAE,QAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,UAAIG,KAAE,IAAEH,GAAE,CAAC;AAAE,aAAO,IAAEG,KAAEH,MAAGC,OAAI,MAAIE,MAAG,CAAC,EAAE,KAAGA,OAAI,EAAE,KAAGA,MAAG,EAAE,KAAGA,MAAIF,MAAG,GAAGD,IAAE,KAAGG,EAAC,GAAE,IAAEA,MAAG,OAAO,OAAOH,EAAC,GAAEA,MAAG,GAAGA,IAAE,IAAG,IAAEG,KAAE,KAAGD,IAAE,MAAG,IAAE;AAAA,IAAE;AAAC,WAAOF,GAAE,MAAI,KAAGA,KAAE,KAAGG,KAAE,KAAGD,KAAEF,GAAE,GAAG,CAAC,KAAGA,KAAE,IAAIA,GAAE,YAAY,GAAGE,IAAEC,IAAE,IAAE,CAAC,IAAEH,cAAa,MAAI,EAAE,IAAEA,GAAE,OAAKE,KAAE,GAAGF,GAAE,GAAG,EAAE,CAAC,GAAEA,KAAE,IAAI,GAAGE,IAAEF,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,IAAGA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAED,MAAG,IAAED,KAAE,KAAG,IAAGG,KAAE,CAAC,EAAE,KAAGH;AAAG,SAAOD,MAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAGH,EAAC;AAAE,QAAII,KAAED,GAAE;AAAO,UAAME,KAAE,MAAIJ,KAAEE,GAAEC,KAAE,CAAC,IAAE;AAAO,SAAIA,MAAGC,KAAE,KAAG,GAAEJ,KAAE,MAAIA,KAAE,IAAE,GAAEA,KAAEG,IAAEH,KAAI,CAAAE,GAAEF,EAAC,IAAEC,GAAEC,GAAEF,EAAC,CAAC;AAAE,QAAGI,IAAE;AAAC,MAAAJ,KAAEE,GAAEF,EAAC,IAAE,CAAC;AAAE,iBAAUD,MAAKK,GAAE,CAAAJ,GAAED,EAAC,IAAEE,GAAEG,GAAEL,EAAC,CAAC;AAAA,IAAC;AAAC,YAAOA,KAAE,GAAGA,EAAC,OAAKG,GAAE,CAAC,IAAE,GAAGH,EAAC,IAAGG;AAAA,EAAC,GAAEH,IAAEC,KAAG,CAAAD,OAAG,GAAGA,IAAEI,IAAED,EAAC,EAAE,GAAE,GAAGH,IAAE,MAAIE,KAAE,IAAE,EAAE,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAED,GAAE,GAAEE,KAAE,IAAED,GAAE,CAAC;AAAE,SAAO,IAAEC,KAAE,IAAIF,GAAE,YAAY,GAAGC,IAAEC,IAAE,KAAE,CAAC,IAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,GAAGD,KAAEA,GAAE,GAAE,IAAEA,GAAE,CAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,OAAKA,GAAE,QAAO;AAAK,QAAMC,KAAED,MAAG,MAAID,KAAE,IAAE,KAAIG,KAAEJ,GAAE,SAAO;AAAE,SAAOG,MAAGC,MAAG,MAAIH,KAAED,GAAEI,EAAC,EAAEF,EAAC,IAAEC,MAAGC,KAAEJ,GAAEG,EAAC,IAAE;AAAM;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAII,KAAE,IAAED,GAAE,CAAC;AAAE,SAAO,GAAGC,EAAC,GAAE,GAAGD,IAAEC,IAAEH,IAAEC,EAAC,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,MAAIH,KAAE,IAAE,IAAGI,KAAEH,KAAEE;AAAE,MAAII,KAAER,GAAE,SAAO;AAAE,SAAOK,MAAGG,MAAG,MAAIP,MAAGD,GAAEQ,EAAC,EAAEN,EAAC,IAAEC,IAAEF,MAAGI,MAAGG,MAAGR,GAAEK,EAAC,IAAEF,IAAEF,OAAI,WAASE,OAAID,OAAIM,KAAEP,MAAG,KAAG,QAAM,aAAW,QAAME,OAAIH,GAAEQ,KAAEJ,EAAC,IAAE,EAAC,CAACF,EAAC,GAAEC,GAAC,GAAE,GAAGH,IAAEC,MAAG,GAAG,KAAGD,GAAEK,EAAC,IAAEF,KAAGF;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGF,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAMG,KAAE,GAAGH,IAAEE,IAAED,EAAC,GAAEG,KAAE,GAAGD,EAAC;AAAE,SAAO,QAAMC,MAAGA,OAAID,MAAG,GAAGH,IAAEE,IAAED,IAAEG,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAE,KAAGD,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAME,KAAE,GAAGF,IAAEC,IAAE,CAAC,GAAEE,KAAE,GAAGD,IAAE,IAAE;AAAE,SAAO,QAAMC,MAAGA,OAAID,MAAG,GAAGF,IAAEC,IAAE,GAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,WAAS,KAAG,IAAE;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEL,GAAE,GAAEQ,KAAE,KAAGR,KAAE,IAAEK,GAAE,CAAC,KAAG,IAAEF;AAAE,EAAAC,KAAE,CAAC,CAACA;AAAE,MAAIK,KAAE,KAAGN,KAAE,GAAGE,IAAEL,IAAEC,EAAC,GAAG,CAAC;AAAE,MAAG,EAAE,IAAEQ,KAAG;AAAC,QAAEA,OAAIN,KAAE,GAAGA,EAAC,GAAEM,KAAE,GAAGA,IAAET,EAAC,GAAEA,KAAE,GAAGK,IAAEL,IAAEC,IAAEE,EAAC;AAAG,QAAIC,KAAE,GAAEI,KAAE;AAAE,WAAKJ,KAAED,GAAE,QAAOC,MAAI;AAAC,YAAMJ,KAAEE,GAAEC,GAAEC,EAAC,CAAC;AAAE,cAAMJ,OAAIG,GAAEK,IAAG,IAAER;AAAA,IAAE;AAAC,IAAAQ,KAAEJ,OAAID,GAAE,SAAOK,KAAGC,KAAE,GAAGA,IAAET,EAAC,GAAEE,KAAE,SAAO,KAAGO,KAAGA,KAAEP,MAAG,OAAM,GAAGC,IAAEM,EAAC,GAAE,IAAEA,MAAG,OAAO,OAAON,EAAC;AAAA,EAAC;AAAC,SAAO,MAAIK,MAAG,MAAIA,MAAG,KAAGC,KAAE,GAAGA,EAAC,MAAIL,KAAEK,IAAEA,MAAG,GAAEA,OAAIL,MAAG,GAAGD,IAAEM,EAAC,GAAE,OAAO,OAAON,EAAC,MAAI,MAAIK,MAAG,GAAGC,EAAC,MAAIN,KAAE,GAAGA,EAAC,GAAEM,KAAE,GAAGA,IAAET,EAAC,GAAES,KAAE,GAAGA,IAAET,IAAEI,EAAC,GAAE,GAAGD,IAAEM,EAAC,GAAET,KAAE,GAAGK,IAAEL,IAAEC,IAAEE,EAAC,IAAG,GAAGM,EAAC,MAAIR,KAAEQ,IAAEA,KAAE,GAAGA,IAAET,IAAEI,EAAC,GAAEK,OAAIR,MAAG,GAAGE,IAAEM,EAAC,KAAIN;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,SAAOF,KAAE,GAAGA,IAAEC,IAAEC,EAAC,GAAE,MAAM,QAAQF,EAAC,IAAEA,KAAE;AAAE;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,MAAID,OAAIA,KAAE,GAAGA,IAAEC,EAAC,IAAG,IAAED;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAM,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,IAAEA,OAAI,CAAC,EAAE,OAAKA;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,EAAAA,KAAE,GAAGA,EAAC;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC,IAAE,GAAGD,GAAEC,EAAC,CAAC;AAAE,UAAM,QAAQC,GAAE,CAAC,CAAC,MAAIA,GAAE,CAAC,IAAE,GAAGA,GAAE,CAAC,CAAC;AAAA,EAAE;AAAC,SAAOF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGJ,KAAEA,GAAE,GAAG,CAAC;AAAE,KAAGI,EAAC,GAAE,GAAGJ,IAAEI,IAAEH,KAAG,QAAME,KAAE,MAAI,OAAOD,EAAC,IAAEA,OAAIC,MAAG,SAAOD,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGF,EAAC;AAAE,MAAIG,KAAE,GAAGJ,IAAEC,IAAEC,EAAC;AAAE,QAAMG,KAAED,OAAI;AAAG,MAAG,KAAGH,MAAG,EAAE,OAAKA,OAAI,CAACI,IAAE;AAAC,UAAMG,KAAEH,KAAE,IAAED,GAAE,CAAC,IAAE;AAAE,QAAIK,KAAED;AAAE,KAAC,CAACH,MAAG,IAAEI,MAAG,GAAGA,EAAC,KAAG,IAAEA,MAAG,EAAE,KAAGA,SAAML,KAAE,GAAGA,EAAC,GAAEK,KAAE,GAAGA,IAAER,EAAC,GAAEA,KAAE,GAAGD,IAAEC,IAAEC,IAAEE,EAAC,IAAGK,KAAE,MAAI,GAAGA,IAAER,EAAC,GAAEQ,KAAE,GAAGN,KAAE,MAAIM,KAAE,KAAGA,IAAER,IAAE,IAAE,GAAEQ,OAAID,MAAG,GAAGJ,IAAEK,EAAC;AAAA,EAAC;AAAC,SAAOL;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAE;AAAC,MAAIC,KAAE;AAAG,SAAO,GAAG,GAAGF,KAAEA,GAAE,CAAC,GAAEA,IAAE,IAAEA,GAAE,CAAC,GAAEE,EAAC,MAAID,KAAEA,KAAE;AAAE;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG,EAAE,QAAOA,GAAE,CAAC,MAAIA,GAAE,CAAC,IAAE,oBAAI;AAAK,MAAG,KAAKA,GAAE,QAAOA,GAAE,CAAC;AAAE,QAAMC,KAAE,oBAAI;AAAI,SAAO,OAAO,eAAeD,IAAE,GAAE,EAAC,OAAMC,GAAC,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGJ,EAAC,GAAEK,KAAE,GAAGD,IAAEJ,IAAEC,IAAEC,EAAC;AAAE,SAAOG,OAAIF,OAAIE,OAAIJ,KAAE,GAAGD,IAAEC,IAAEI,EAAC,IAAGD,GAAE,IAAIF,IAAEC,EAAC,IAAGF;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEJ,GAAE,IAAIG,EAAC;AAAE,MAAG,QAAMC,GAAE,QAAOA;AAAE,EAAAA,KAAE;AAAE,WAAQJ,KAAE,GAAEA,KAAEG,GAAE,QAAOH,MAAI;AAAC,UAAMK,KAAEF,GAAEH,EAAC;AAAE,YAAM,GAAGC,IAAEC,IAAEG,EAAC,MAAI,MAAID,OAAIF,KAAE,GAAGD,IAAEC,IAAEE,EAAC,IAAGA,KAAEC;AAAA,EAAE;AAAC,SAAOL,GAAE,IAAIG,IAAEC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,IAAEH,GAAE,CAAC;AAAE,QAAMI,KAAE,GAAGJ,IAAEG,IAAED,EAAC;AAAE,MAAIG;AAAE,MAAG,QAAMD,MAAGA,GAAE,MAAI,GAAG,SAAOH,KAAE,GAAGG,EAAC,OAAKA,MAAG,GAAGJ,IAAEG,IAAED,IAAED,EAAC,GAAEA,GAAE;AAAE,MAAG,MAAM,QAAQG,EAAC,GAAE;AAAC,UAAMJ,KAAE,IAAEI,GAAE,CAAC;AAAE,IAAAC,KAAE,IAAEL,KAAE,GAAG,GAAGI,IAAEJ,IAAE,KAAE,GAAEC,IAAE,IAAE,IAAE,KAAGD,KAAEI,KAAE,GAAGC,IAAEJ,IAAE,IAAE;AAAA,EAAC,MAAM,CAAAI,KAAE,GAAG,QAAOJ,IAAE,IAAE;AAAE,SAAOI,OAAID,MAAG,GAAGJ,IAAEG,IAAED,IAAEG,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGL,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAGH,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAMI,KAAE,GAAGJ,IAAEG,IAAED,EAAC;AAAE,UAAOD,KAAE,GAAGG,IAAEH,IAAE,OAAGE,EAAC,OAAKC,MAAG,QAAMH,MAAG,GAAGD,IAAEG,IAAED,IAAED,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAGD,IAAEC,IAAEC,EAAC,GAAG,QAAOD;AAAE,MAAIE,KAAE,KAAGH,KAAEA,GAAE,GAAG,CAAC;AAAE,MAAG,EAAE,IAAEG,KAAG;AAAC,UAAMC,KAAE,GAAGH,EAAC;AAAE,IAAAG,OAAIH,MAAG,GAAGD,IAAEG,IAAED,IAAED,KAAEG,EAAC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,EAAAR,KAAEA,GAAE;AAAE,MAAIS,KAAE,CAAC,EAAE,IAAER;AAAG,QAAMK,KAAEG,KAAE,IAAEL;AAAE,EAAAC,KAAE,CAAC,CAACA,IAAEG,OAAI,CAACC;AAAE,MAAIF,KAAE,KAAGH,KAAE,GAAGJ,IAAEC,IAAEE,EAAC,GAAG,CAAC;AAAE,MAAG,EAAEM,KAAE,CAAC,EAAE,IAAEF,MAAI;AAAC,QAAIG,KAAEN,IAAE,IAAEH;AAAE,UAAMD,KAAE,CAAC,EAAE,KAAGO,KAAE,GAAGA,IAAEN,EAAC;AAAI,IAAAD,OAAI,KAAG;AAAG,QAAIG,KAAE,CAACH,IAAEK,KAAE,MAAGG,KAAE,GAAEC,KAAE;AAAE,WAAKD,KAAEE,GAAE,QAAOF,MAAI;AAAC,YAAMP,KAAE,GAAGS,GAAEF,EAAC,GAAEN,IAAE,OAAG,CAAC;AAAE,UAAGD,cAAaC,IAAE;AAAC,YAAG,CAACF,IAAE;AAAC,gBAAMA,KAAE,CAAC,EAAE,KAAG,IAAEC,GAAE,EAAE,CAAC;AAAI,UAAAE,OAAI,CAACH,IAAEK,OAAIL;AAAA,QAAC;AAAC,QAAAU,GAAED,IAAG,IAAER;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAQ,KAAED,OAAIE,GAAE,SAAOD,KAAGF,MAAG,GAAEA,KAAEF,KAAE,KAAGE,KAAE,MAAIA,IAAE,GAAGG,IAAEH,KAAEJ,KAAE,IAAEI,KAAE,KAAGA,EAAC,GAAEP,MAAG,OAAO,OAAOU,EAAC;AAAA,EAAC;AAAC,MAAGF,MAAG,EAAE,IAAED,MAAG,CAACH,GAAE,WAAS,MAAIE,MAAG,MAAIA,MAAG,KAAGC,MAAI;AAAC,SAAI,GAAGA,EAAC,MAAIH,KAAE,GAAGA,EAAC,GAAEG,KAAE,GAAGA,IAAEN,EAAC,GAAEA,KAAE,GAAGD,IAAEC,IAAEE,IAAEC,EAAC,IAAGF,KAAEE,IAAEI,KAAED,IAAEG,KAAE,GAAEA,KAAER,GAAE,QAAOQ,KAAI,EAACH,KAAEL,GAAEQ,EAAC,QAAM,IAAE,GAAGH,EAAC,OAAKL,GAAEQ,EAAC,IAAE;AAAG,IAAAF,MAAG,GAAE,GAAGN,IAAEM,KAAEN,GAAE,SAAO,MAAIM,KAAE,KAAGA,EAAC,GAAED,KAAEC;AAAA,EAAC;AAAC,SAAO,MAAIF,MAAG,MAAIA,MAAG,KAAGC,KAAE,GAAGA,EAAC,MAAIN,KAAEM,KAAGA,MAAG,CAACH,GAAE,UAAQ,KAAGG,OAAI,CAACE,MAAG,KAAGF,MAAG,IAAE,UAAQN,MAAG,GAAGG,IAAEG,EAAC,GAAE,OAAO,OAAOH,EAAC,MAAI,MAAIE,MAAG,GAAGC,EAAC,MAAI,GAAGH,KAAE,GAAGA,EAAC,GAAEG,KAAE,GAAGA,KAAE,GAAGA,IAAEN,EAAC,GAAEA,IAAEI,EAAC,CAAC,GAAEJ,KAAE,GAAGD,IAAEC,IAAEE,IAAEC,EAAC,IAAG,GAAGG,EAAC,MAAIJ,KAAEI,KAAGA,KAAE,GAAGA,IAAEN,IAAEI,EAAC,OAAKF,MAAG,GAAGC,IAAEG,EAAC,KAAIH;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAEH,GAAE,EAAE,CAAC;AAAE,SAAO,GAAGA,IAAEG,IAAEF,IAAEC,IAAE,GAAG,GAAE,OAAG,EAAE,IAAEC,GAAE;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,QAAMA,OAAIA,KAAE,SAAQ,GAAGH,IAAEE,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMA,OAAIA,KAAE;AAAQ,KAAE;AAAC,QAAIC,KAAE,KAAGJ,KAAEA,GAAE,GAAG,CAAC;AAAE,QAAG,GAAGI,EAAC,GAAE,QAAMD,IAAE;AAAC,YAAMA,KAAE,GAAGH,EAAC;AAAE,UAAG,GAAGG,IAAEH,IAAEI,IAAEF,EAAC,MAAID,GAAE,OAAM;AAAE,MAAAE,GAAE,IAAID,IAAE,CAAC;AAAA,IAAC,MAAM,CAAAE,KAAE,GAAGJ,IAAEI,IAAEF,IAAED,EAAC;AAAE,OAAGD,IAAEI,IAAEH,IAAEE,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAE;AAAC,SAAM,SAAOD,KAAE,MAAI,IAAEC,KAAE,IAAED,KAAE,KAAGA;AAAG;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAGD,MAAGC,OAAIF,MAAG,MAAKA;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,KAAG,IAAEF,GAAE,EAAE,CAAC,CAAC,GAAE,GAAGA,IAAEC,IAAE,IAAG,GAAE,IAAE,EAAE,KAAK,GAAGC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,IAAEJ,GAAE,EAAE,CAAC;AAAE,KAAGI,EAAC,GAAEJ,KAAE,GAAGA,IAAEI,IAAEF,IAAED,IAAE,GAAE,IAAE,GAAEE,KAAE,QAAMA,KAAEA,KAAE,IAAID,MAAEF,GAAE,KAAKG,EAAC,GAAEH,GAAE,CAAC,IAAE,KAAG,IAAEG,GAAE,EAAE,CAAC,KAAG,KAAGH,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,GAAG,GAAGD,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,GAAG,GAAGD,IAAEC,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,GAAGD,IAAEC,EAAC,KAAG;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,aAAW,OAAOA,GAAE,OAAMF,KAAE,OAAOE,IAAE,MAAM,4BAA4B,YAAUF,KAAEA,KAAEE,KAAE,MAAM,QAAQA,EAAC,IAAE,UAAQF,KAAE,MAAM,KAAKE,EAAC,EAAE;AAAE,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA,GAAE,OAAM,EAAE,OAAO;AAAE,QAAG,CAAC,GAAGA,EAAC,EAAE,OAAM,EAAE,OAAO;AAAE,IAAAA,MAAG;AAAA,EAAC;AAAC,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMA,MAAG,YAAU,OAAOA,GAAE,OAAM,MAAM,uDAAuD,OAAOA,EAAC,KAAKA,EAAC,EAAE;AAAE,KAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC;AAAC,UAAMM,KAAER,GAAE;AAAE,QAAIS,KAAE,IAAED,GAAE,CAAC;AAAE,QAAG,GAAGC,EAAC,GAAE,QAAMP,GAAE,IAAGM,IAAEC,IAAER,EAAC;AAAA,SAAM;AAAC,UAAIE,KAAEH,KAAE,IAAEE,GAAE,CAAC,GAAEE,KAAE,GAAGJ,EAAC,GAAEK,KAAED,MAAG,OAAO,SAASF,EAAC;AAAE,WAAIE,OAAIJ,KAAE,IAAGK,OAAIH,KAAE,GAAGA,EAAC,GAAEC,KAAE,GAAEH,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,GAAEJ,KAAE,QAAIL,MAAG,IAAGI,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAI;AAAC,cAAMH,KAAEC,GAAEE,EAAC,GAAEI,KAAE,GAAGP,EAAC;AAAE,eAAO,GAAGA,IAAEO,EAAC,MAAIH,OAAIH,KAAE,GAAGA,EAAC,GAAEC,KAAE,GAAEH,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,GAAEJ,KAAE,QAAIH,GAAEE,EAAC,IAAEI;AAAA,MAAE;AAAC,MAAAR,OAAIG,OAAIE,OAAIH,KAAE,GAAGA,EAAC,GAAEF,KAAE,GAAGA,KAAE,GAAGA,IAAES,EAAC,GAAEA,IAAE,IAAE,IAAG,GAAGP,IAAEF,EAAC,IAAG,GAAGQ,IAAEC,IAAER,IAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,SAAO,MAAM,sBAAsBD,EAAC,iBAAiBC,EAAC,GAAG;AAAC;AAAC,SAAS,KAAI;AAAC,SAAO,MAAM,6CAA6C;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,MAAM,0CAA0CA,EAAC,MAAMD,EAAC,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,YAAU,OAAOA,GAAE,QAAM,EAAC,QAAO,EAAEA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAG,MAAM,QAAQA,EAAC,EAAE,QAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc,WAAW,QAAM,EAAC,QAAOA,IAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc,YAAY,QAAM,EAAC,QAAO,IAAI,WAAWA,EAAC,GAAE,GAAE,MAAE;AAAE,MAAGA,GAAE,gBAAc,EAAE,QAAM,EAAC,QAAO,EAAEA,EAAC,KAAG,IAAI,WAAW,CAAC,GAAE,GAAE,KAAE;AAAE,MAAGA,cAAa,WAAW,QAAM,EAAC,QAAO,IAAI,WAAWA,GAAE,QAAOA,GAAE,YAAWA,GAAE,UAAU,GAAE,GAAE,MAAE;AAAE,QAAM,MAAM,2IAA2I;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAIC,IAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,QAAMG,KAAER,GAAE;AAAE,MAAIS,KAAET,GAAE;AAAE,KAAE;AAAC,IAAAE,KAAEM,GAAEC,IAAG,GAAEN,OAAI,MAAID,OAAIG,IAAEA,MAAG;AAAA,EAAC,SAAOA,KAAE,MAAI,MAAIH;AAAG,OAAIG,KAAE,OAAKD,OAAI,MAAIF,OAAI,IAAGG,KAAE,GAAEA,KAAE,MAAI,MAAIH,IAAEG,MAAG,EAAE,CAAAH,KAAEM,GAAEC,IAAG,GAAEL,OAAI,MAAIF,OAAIG;AAAE,MAAG,GAAGL,IAAES,EAAC,GAAEP,KAAE,IAAI,QAAOD,GAAEE,OAAI,GAAEC,OAAI,CAAC;AAAE,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAE,GAAEC,KAAEF,GAAE;AAAE,QAAMG,KAAED,KAAE,IAAGE,KAAEJ,GAAE;AAAE,SAAKE,KAAEC,MAAG;AAAC,UAAMA,KAAEC,GAAEF,IAAG;AAAE,QAAGD,MAAGE,IAAE,MAAI,MAAIA,IAAG,QAAO,GAAGH,IAAEE,EAAC,GAAE,CAAC,EAAE,MAAID;AAAA,EAAE;AAAC,QAAM,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAE,MAAIE,KAAEF,GAAE,GAAEG,KAAEF,GAAEC,IAAG,GAAEE,KAAE,MAAID;AAAE,MAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,GAAE,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,OAAI,MAAID,OAAI,IAAG,MAAIA,OAAIA,KAAEF,GAAEC,IAAG,GAAEE,MAAGD,MAAG,IAAG,MAAIA,MAAG,MAAIF,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,KAAG,MAAID,GAAEC,IAAG,MAAM,OAAM,GAAG;AAAE,SAAO,GAAGF,IAAEE,EAAC,GAAEE;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,GAAGA,EAAC,MAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE,QAAME,KAAEF,GAAE,GAAEG,KAAEF,GAAEC,EAAC,GAAEE,KAAEH,GAAEC,KAAE,CAAC,GAAEG,KAAEJ,GAAEC,KAAE,CAAC;AAAE,SAAOD,KAAEA,GAAEC,KAAE,CAAC,GAAE,GAAGF,IAAEA,GAAE,IAAE,CAAC,IAAGG,MAAG,IAAEC,MAAG,IAAEC,MAAG,KAAGJ,MAAG,QAAM;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,EAAC;AAAE,EAAAA,KAAE,KAAGC,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAI,SAAOA,MAAG,SAAQ,OAAKC,KAAED,KAAE,MAAID,MAAG,IAAE,KAAG,KAAGE,KAAE,uBAAqBF,KAAEC,KAAED,KAAE,KAAK,IAAI,GAAEE,KAAE,GAAG,KAAGD,KAAE;AAAQ;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,GAAGA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE,EAAC,IAAGC,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,EAAAF,GAAE,KAAGE,IAAED,OAAIA,KAAE,GAAGA,EAAC,GAAED,GAAE,IAAEC,GAAE,QAAOD,GAAE,IAAEC,GAAE,GAAED,GAAE,IAAE,GAAEA,GAAE,IAAEA,GAAE,EAAE,QAAOA,GAAE,IAAEA,GAAE;AAAE;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,IAAEC,IAAEA,KAAED,GAAE,EAAE,OAAM,GAAGA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAGA,KAAE,EAAE,OAAM,MAAM,yCAAyCA,EAAC,EAAE;AAAE,QAAMC,KAAEF,GAAE,GAAEG,KAAED,KAAED;AAAE,MAAGE,KAAEH,GAAE,EAAE,OAAM,GAAGC,IAAED,GAAE,IAAEE,EAAC;AAAE,SAAOF,GAAE,IAAEG,IAAED;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,MAAG,KAAGA,GAAE,QAAO,EAAE;AAAE,MAAIC,KAAE,GAAGF,IAAEC,EAAC;AAAE,SAAOD,GAAE,MAAIA,GAAE,IAAEE,KAAEF,GAAE,EAAE,SAASE,IAAEA,KAAED,EAAC,KAAGD,KAAEA,GAAE,GAAEE,KAAEA,QAAKD,KAAEC,KAAED,MAAG,IAAI,WAAW,CAAC,IAAE,KAAGD,GAAE,MAAME,IAAED,EAAC,IAAE,IAAI,WAAWD,GAAE,SAASE,IAAED,EAAC,CAAC,IAAG,KAAGC,GAAE,SAAO,EAAE,IAAE,IAAI,EAAEA,IAAE,CAAC;AAAC;AAAC,GAAG,UAAU,SAAO;AAAO,IAAI,KAAG,CAAC;AAAE,SAAS,GAAGF,IAAE;AAAC,MAAIC,KAAED,GAAE;AAAE,MAAGC,GAAE,KAAGA,GAAE,EAAE,QAAM;AAAG,EAAAD,GAAE,IAAEA,GAAE,EAAE;AAAE,MAAIE,KAAE,GAAGF,GAAE,CAAC;AAAE,MAAGC,KAAEC,OAAI,GAAE,GAAGA,MAAG,MAAI,KAAGA,MAAG,GAAG,OAAM,GAAGA,IAAEF,GAAE,CAAC;AAAE,MAAGC,KAAE,EAAE,OAAM,MAAM,yBAAyBA,EAAC,iBAAiBD,GAAE,CAAC,GAAG;AAAE,SAAOA,GAAE,IAAEC,IAAED,GAAE,IAAEE,IAAE;AAAE;AAAC,SAAS,GAAGF,IAAE;AAAC,UAAOA,GAAE,GAAE;AAAA,IAAC,KAAK;AAAE,WAAGA,GAAE,IAAE,GAAGA,EAAC,IAAE,GAAGA,GAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,SAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,UAAG,KAAGA,GAAE,EAAE,IAAGA,EAAC;AAAA,WAAM;AAAC,YAAIC,KAAE,GAAGD,GAAE,CAAC;AAAE,WAAGA,KAAEA,GAAE,GAAEA,GAAE,IAAEC,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM,KAAK;AAAE,SAAGD,KAAEA,GAAE,GAAEA,GAAE,IAAE,CAAC;AAAE;AAAA,IAAM,KAAK;AAAE,WAAIC,KAAED,GAAE,OAAI;AAAC,YAAG,CAAC,GAAGA,EAAC,EAAE,OAAM,MAAM,uCAAuC;AAAE,YAAG,KAAGA,GAAE,GAAE;AAAC,cAAGA,GAAE,KAAGC,GAAE,OAAM,MAAM,yBAAyB;AAAE;AAAA,QAAK;AAAC,WAAGD,EAAC;AAAA,MAAC;AAAC;AAAA,IAAM;AAAQ,YAAM,GAAGA,GAAE,GAAEA,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,EAAE,GAAEI,KAAE,GAAGJ,GAAE,CAAC,GAAEK,KAAEL,GAAE,EAAE,IAAEI;AAAE,MAAII,KAAEH,KAAEF;AAAE,MAAGK,MAAG,MAAIR,GAAE,EAAE,IAAEK,IAAEH,GAAED,IAAED,IAAE,QAAO,QAAO,MAAM,GAAEQ,KAAEH,KAAEL,GAAE,EAAE,IAAGQ,GAAE,OAAM,MAAM,wDAAwDJ,EAAC,wBAAwBA,KAAEI,EAAC,sFAAsF;AAAE,SAAOR,GAAE,EAAE,IAAEK,IAAEL,GAAE,EAAE,IAAEG,IAAEF;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIQ,KAAE,GAAGR,GAAE,CAAC,GAAES,KAAE,GAAGT,KAAEA,GAAE,GAAEQ,EAAC;AAAE,MAAGR,KAAEA,GAAE,GAAE,GAAE;AAAC,QAAIM,IAAEC,KAAEP;AAAE,KAACM,KAAE,OAAKA,KAAE,IAAE,IAAI,YAAY,SAAQ,EAAC,OAAM,KAAE,CAAC,IAAGE,KAAEC,KAAED,IAAED,KAAE,MAAIE,MAAGD,OAAID,GAAE,SAAOA,KAAEA,GAAE,SAASE,IAAED,EAAC;AAAE,QAAG;AAAC,UAAIE,KAAEJ,GAAE,OAAOC,EAAC;AAAA,IAAC,SAAOP,IAAE;AAAC,UAAG,WAAS,GAAE;AAAC,YAAG;AAAC,UAAAM,GAAE,OAAO,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC;AAAA,QAAC,SAAON,IAAE;AAAA,QAAC;AAAC,YAAG;AAAC,UAAAM,GAAE,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC,GAAE,IAAE;AAAA,QAAE,SAAON,IAAE;AAAC,cAAE;AAAA,QAAE;AAAA,MAAC;AAAC,YAAK,CAAC,MAAI,IAAE,SAAQA;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,IAAAQ,MAAGE,KAAED,MAAGD,IAAEC,KAAE,CAAC;AAAE,QAAIN,IAAEC,KAAE;AAAK,WAAKM,KAAEF,MAAG;AAAC,UAAI,IAAER,GAAEU,IAAG;AAAE,UAAE,MAAID,GAAE,KAAK,CAAC,IAAE,IAAE,MAAIC,MAAGF,KAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,IAAE,OAAK,QAAM,MAAIP,OAAIO,MAAI,EAAE,KAAGD,GAAE,MAAM,KAAG,MAAI,IAAE,KAAGN,EAAC,KAAG,IAAE,MAAIO,MAAGF,KAAE,IAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,QAAM,MAAIP,OAAI,QAAM,KAAGA,KAAE,OAAK,QAAM,KAAGA,MAAG,OAAK,QAAM,OAAKG,KAAEN,GAAEU,IAAG,OAAKA,MAAI,EAAE,KAAGD,GAAE,MAAM,KAAG,MAAI,MAAI,KAAGN,OAAI,IAAE,KAAGG,EAAC,KAAG,KAAG,MAAII,MAAGF,KAAE,IAAE,EAAE,KAAGL,KAAEH,GAAEU,IAAG,GAAE,QAAM,MAAIP,OAAIA,KAAE,OAAK,KAAG,OAAK,MAAI,KAAG,QAAM,OAAKG,KAAEN,GAAEU,IAAG,OAAK,QAAM,OAAKH,KAAEP,GAAEU,IAAG,OAAKA,MAAI,EAAE,MAAI,KAAG,IAAE,MAAI,MAAI,KAAGP,OAAI,MAAI,KAAGG,OAAI,IAAE,KAAGC,IAAE,KAAG,OAAME,GAAE,KAAK,SAAO,KAAG,KAAG,OAAM,SAAO,OAAK,EAAE,MAAI,EAAE,GAAEA,GAAE,UAAQ,SAAOL,KAAE,EAAEA,IAAEK,EAAC,GAAEA,GAAE,SAAO;AAAA,IAAE;AAAC,IAAAC,KAAE,EAAEN,IAAEK,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGV,IAAE;AAAC,QAAMC,KAAE,GAAGD,GAAE,CAAC;AAAE,SAAO,GAAGA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,GAAGH,GAAE,CAAC;AAAE,OAAIG,KAAEH,GAAE,EAAE,IAAEG,IAAEH,GAAE,EAAE,IAAEG,KAAG,CAAAD,GAAE,KAAKD,GAAED,GAAE,CAAC,CAAC;AAAC;AAAC,IAAI,KAAG,CAAC;AAAE,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAEA,GAAE,EAAED,IAAEC,GAAE,GAAEA,GAAE,GAAEC,EAAC,IAAED,GAAE,EAAED,IAAEC,GAAE,GAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,SAAK,IAAE,GAAGD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAG;AAAC,UAAID,KAAE,GAAG,IAAI;AAAA,IAAC,UAAC;AAAQ,WAAG;AAAA,IAAM;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIA,KAAE;AAAG,WAAOA,GAAE,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,CAAC,IAAEA,GAAE,EAAE,MAAKA,GAAE,GAAEA,GAAE,YAAY;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,KAAK;AAAE,WAAO,IAAI,KAAK,YAAY,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAE,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,EAAE,KAAG,IAAE,KAAK,EAAE,CAAC;AAAA,EAAG;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,QAAQ,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,OAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,GAAG,UAAU,IAAE,IAAG,GAAG,UAAU,WAAS,WAAU;AAAC,SAAO,KAAK,EAAE,SAAS;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAED,OAAI,GAAE,KAAK,IAAEC,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGD,IAAE;AAAC,SAAOA,KAAE,UAAU,KAAKA,EAAC,KAAG,GAAGA,EAAC,GAAE,IAAI,GAAG,IAAG,EAAE,KAAG,OAAK,OAAK,IAAI,GAAG,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAED,OAAI,GAAE,KAAK,IAAEC,OAAI;AAAA,EAAC;AAAC;AAAE,IAAI;AAAG,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAKA,KAAE,KAAGD,KAAE,MAAK,CAAAD,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,MAAGA,OAAI,IAAEC,MAAG,QAAM,GAAEA,QAAK;AAAE,EAAAF,GAAE,EAAE,KAAKC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAKA,KAAE,MAAK,CAAAD,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK;AAAE,EAAAD,GAAE,EAAE,KAAKC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAGA,MAAG,EAAE,IAAGD,IAAEC,EAAC;AAAA,OAAM;AAAC,aAAQC,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAF,GAAE,EAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,OAAI;AAAE,IAAAD,GAAE,EAAE,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,GAAE,EAAE,KAAKC,OAAI,IAAE,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,IAAE,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,KAAG,GAAG,GAAED,GAAE,EAAE,KAAKC,OAAI,KAAG,GAAG;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,QAAIA,GAAE,WAASD,GAAE,EAAE,KAAKC,EAAC,GAAED,GAAE,KAAGC,GAAE;AAAO;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,KAAGF,GAAE,GAAE,IAAEC,KAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,SAAO,GAAGD,IAAEC,IAAE,CAAC,GAAEA,KAAED,GAAE,EAAE,IAAI,GAAE,GAAGA,IAAEC,EAAC,GAAEA,GAAE,KAAKD,GAAE,CAAC,GAAEC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAIC,KAAED,GAAE,IAAI;AAAE,OAAIC,KAAEF,GAAE,IAAEA,GAAE,EAAE,OAAO,IAAEE,IAAEA,KAAE,MAAK,CAAAD,GAAE,KAAK,MAAIC,KAAE,GAAG,GAAEA,QAAK,GAAEF,GAAE;AAAI,EAAAC,GAAE,KAAKC,EAAC,GAAEF,GAAE;AAAG;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAEC,IAAE,CAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,MAAM,GAAE,GAAGF,IAAEA,GAAE,EAAE,IAAI,CAAC,GAAE,GAAGA,IAAEE,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMD,OAAID,KAAE,GAAGD,IAAEC,EAAC,GAAEE,GAAED,IAAEF,EAAC,GAAE,GAAGA,IAAEC,EAAC;AAAE;AAAC,SAAS,KAAI;AAAC,QAAMD,KAAE,MAAK;AAAA,IAAC,cAAa;AAAC,YAAM,MAAM;AAAA,IAAC;AAAA,EAAC;AAAE,SAAO,OAAO,eAAeA,IAAEA,GAAE,SAAS,GAAEA;AAAC;AAAC,IAAI,KAAG,GAAG;AAAV,IAAY,KAAG,GAAG;AAAlB,IAAoB,KAAG,GAAG;AAA1B,IAA4B,KAAG,GAAG;AAAlC,IAAoC,KAAG,GAAG;AAA1C,IAA4C,KAAG,GAAG;AAAlD,IAAoD,KAAG,GAAG;AAA1D,IAA4D,KAAG,GAAG;AAAlE,IAAoE,KAAG,GAAG;AAA1E,IAA4E,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEF,IAAE,KAAK,IAAEC,IAAED,KAAE,IAAG,KAAK,IAAE,CAAC,CAACA,MAAGE,OAAIF,MAAG;AAAA,EAAE;AAAC;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGD,IAAEC,IAAE,EAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGJ,IAAEE,IAAE,GAAGD,IAAEE,EAAC,GAAEC,EAAC;AAAC;AAAC,IAAM,KAAG,IAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAI,GAAGA,IAAE,GAAGC,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,IAAG,EAAE;AAA5E,IAA8E,KAAG,IAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAI,GAAGA,IAAE,GAAGC,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,IAAG,EAAE;AAAE,IAAI,KAAG,OAAO;AAAd,IAAgB,KAAG,OAAO;AAA1B,IAA4B,KAAG,OAAO;AAAtC,IAAwC,KAAG,OAAO;AAAE,IAAI;AAAJ,IAAO;AAAG,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAED,GAAEH,EAAC;AAAE,MAAGI,GAAE,QAAOA;AAAE,GAACA,KAAE,CAAC,GAAG,KAAGD,IAAEC,GAAE,KAAE,SAASJ,IAAE;AAAC,YAAO,OAAOA,IAAE;AAAA,MAAC,KAAI;AAAU,eAAO,OAAK,CAAC,GAAE,QAAO,IAAE;AAAA,MAAE,KAAI;AAAS,eAAOA,KAAE,IAAE,SAAO,MAAIA,KAAE,OAAK,CAAC,GAAE,MAAM,IAAE,CAAC,CAACA,IAAE,MAAM;AAAA,MAAE,KAAI;AAAS,eAAM,CAAC,GAAEA,EAAC;AAAA,MAAE,KAAI;AAAS,eAAOA;AAAA,IAAC;AAAA,EAAC,GAAEG,GAAE,CAAC,CAAC;AAAE,MAAIE,KAAEF,GAAE,CAAC;AAAE,MAAIK,KAAE;AAAE,EAAAH,MAAGA,GAAE,gBAAc,WAASD,GAAE,KAAGC,IAAE,cAAY,QAAOA,KAAEF,GAAE,EAAEK,EAAC,OAAKJ,GAAE,KAAG,MAAG,OAAKC,IAAE,OAAKF,GAAEK,KAAE,CAAC,GAAEH,KAAEF,GAAEK,MAAG,CAAC;AAAI,QAAMC,KAAE,CAAC;AAAE,SAAKJ,MAAG,MAAM,QAAQA,EAAC,KAAGA,GAAE,UAAQ,YAAU,OAAOA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,KAAG;AAAC,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAG,GAAEJ,GAAEC,EAAC,CAAC,IAAED;AAAE,IAAAA,KAAEF,GAAE,EAAEK,EAAC;AAAA,EAAC;AAAC,OAAIF,KAAE,GAAE,WAASD,MAAG;AAAC,QAAIL;AAAE,gBAAU,OAAOK,OAAIC,MAAGD,IAAEA,KAAEF,GAAE,EAAEK,EAAC;AAAG,QAAID,KAAE;AAAO,QAAGF,cAAa,KAAGL,KAAEK,MAAGL,KAAE,IAAGQ,OAAKR,IAAG,GAAE;AAAC,MAAAK,KAAEF,GAAE,EAAEK,EAAC,GAAED,KAAEJ;AAAE,UAAIO,KAAEF;AAAE,oBAAY,OAAOH,OAAIA,KAAEA,GAAE,GAAEE,GAAEG,EAAC,IAAEL,KAAGE,KAAEF;AAAA,IAAC;AAAC,SAAIK,KAAEJ,KAAE,GAAE,YAAU,QAAOD,KAAEF,GAAE,EAAEK,EAAC,MAAIH,KAAE,MAAIK,MAAGL,IAAEA,KAAEF,GAAE,EAAEK,EAAC,IAAGF,KAAEI,IAAEJ,MAAI;AAAC,YAAMH,KAAEM,GAAEH,EAAC;AAAE,MAAAC,KAAEL,GAAEE,IAAEE,IAAEN,IAAEO,IAAEJ,EAAC,IAAEF,GAAEG,IAAEE,IAAEN,IAAEG,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAOA,GAAEH,EAAC,IAAEI;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,MAAM,QAAQA,EAAC,IAAEA,GAAE,CAAC,aAAY,KAAGA,KAAE,CAAC,IAAGA,EAAC,IAAE,CAACA,IAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOD,cAAa,KAAGA,GAAE,IAAE,MAAM,QAAQA,EAAC,IAAE,GAAGA,IAAEC,IAAE,KAAE,IAAE;AAAM;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE;AAAE,EAAAF,GAAEC,EAAC,IAAEE,KAAE,CAACH,IAAEC,IAAEC,OAAIE,GAAEJ,IAAEC,IAAEC,IAAEC,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAIM,IAAEC;AAAE,EAAAT,GAAEC,EAAC,IAAE,CAACD,IAAEC,IAAEC,OAAIG,GAAEL,IAAEC,IAAEC,IAAEO,OAAI,GAAG,IAAG,IAAG,IAAGN,EAAC,EAAE,GAAEK,OAAI,GAAGL,EAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,MAAIC,KAAED,GAAE,EAAE;AAAE,MAAG,QAAMC,GAAE,QAAOA;AAAE,QAAMC,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC;AAAE,SAAOC,KAAEC,GAAE,KAAG,CAACF,IAAEC,OAAI,GAAGD,IAAEC,IAAEC,EAAC,IAAE,CAACF,IAAEC,OAAI;AAAC,UAAME,KAAE,IAAEH,GAAE,CAAC;AAAE,WAAK,GAAGC,EAAC,KAAG,KAAGA,GAAE,KAAG;AAAC,UAAIG,KAAEH,GAAE,GAAEI,KAAEH,GAAEE,EAAC;AAAE,UAAG,QAAMC,IAAE;AAAC,YAAIG,KAAEN,GAAE;AAAG,QAAAM,OAAIA,KAAEA,GAAEJ,EAAC,OAAK,SAAOI,KAAE,GAAGA,EAAC,OAAKH,KAAEH,GAAEE,EAAC,IAAEI;AAAA,MAAG;AAAC,cAAMH,MAAGA,GAAEJ,IAAED,IAAEI,EAAC,MAAIA,MAAGC,KAAEJ,IAAG,GAAE,GAAGI,EAAC,GAAEA,GAAE,KAAGA,KAAE,UAAQG,KAAEH,GAAE,EAAE,IAAED,IAAEC,GAAE,EAAE,IAAED,IAAEC,KAAE,GAAGA,GAAE,GAAEG,EAAC,IAAGJ,KAAEJ,IAAEK,QAAKG,KAAEJ,GAAE,CAAC,KAAGI,GAAE,KAAKH,EAAC,IAAED,GAAE,CAAC,IAAE,CAACC,EAAC;AAAA,IAAG;AAAC,WAAO,OAAKF,MAAG,GAAGH,EAAC,GAAE;AAAA,EAAE,GAAEA,GAAE,EAAE,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,MAAGD,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,MAAGA,KAAEA,GAAE,CAAC,GAAE;AAAC,UAAME,KAAE,GAAGF,EAAC,GAAEG,KAAE,GAAG,IAAG,IAAG,IAAGH,EAAC,EAAE;AAAE,WAAM,CAACA,IAAEI,IAAEC,OAAIJ,GAAED,IAAEI,IAAEC,IAAEF,IAAED,EAAC;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAEC,EAAC,IAAEC,GAAE;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,IAAEC;AAAE,QAAMG,KAAEN,GAAE;AAAE,EAAAF,GAAEC,EAAC,IAAE,CAACD,IAAEC,IAAEC,OAAIM,GAAER,IAAEC,IAAEC,IAAEG,OAAI,GAAG,IAAG,IAAG,IAAGF,EAAC,EAAE,GAAEC,OAAI,GAAGD,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGH,IAAE;AAAC,MAAIC,KAAED,GAAE,EAAE;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC;AAAE,IAAAC,KAAE,CAACD,IAAEC,OAAI,GAAGD,IAAEC,IAAEC,EAAC,GAAEF,GAAE,EAAE,IAAEC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,IAAC,SAASF,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,MAAIF,KAAE,IAAE,IAAGG,KAAEJ,GAAE,QAAOK,KAAED,OAAIH,KAAE,KAAGA,KAAE,MAAIA,KAAE,CAAC,CAACG,MAAG,GAAGJ,GAAEI,KAAE,CAAC,CAAC,KAAG,KAAG;AAAG,aAAQH,KAAE,GAAEA,KAAEI,IAAEJ,KAAI,CAAAC,GAAED,KAAEE,IAAEH,GAAEC,EAAC,CAAC;AAAE,QAAGA,IAAE;AAAC,MAAAD,KAAEA,GAAEI,KAAE,CAAC;AAAE,iBAAUH,MAAKD,GAAE,EAAC,MAAMC,EAAC,KAAGC,GAAE,CAACD,IAAED,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC,GAAED,IAAE,IAAEA,GAAE,CAAC,KAAGE,GAAE,EAAE,CAAC,IAAE,MAAI,KAAI,CAACF,IAAEG,OAAI;AAAC,QAAG,QAAMA,IAAE;AAAC,UAAIC,MAAE,SAASJ,IAAEC,IAAE;AAAC,YAAIC,KAAEF,GAAEC,EAAC;AAAE,YAAGC,GAAE,QAAOA;AAAE,aAAIA,KAAEF,GAAE,QAAME,KAAEA,GAAED,EAAC,IAAG;AAAC,cAAIE,MAAGD,KAAE,GAAGA,EAAC,GAAG,CAAC,EAAE;AAAE,cAAGA,KAAEA,GAAE,CAAC,GAAE;AAAC,kBAAMD,KAAE,GAAGC,EAAC,GAAEE,KAAE,GAAG,IAAG,IAAG,IAAGF,EAAC,EAAE;AAAE,YAAAA,KAAEF,GAAE,KAAG,GAAGI,IAAEH,EAAC,IAAE,CAACD,IAAEE,IAAEG,OAAIF,GAAEH,IAAEE,IAAEG,IAAED,IAAEH,EAAC;AAAA,UAAC,MAAM,CAAAC,KAAEC;AAAE,iBAAOH,GAAEC,EAAC,IAAEC;AAAA,QAAC;AAAA,MAAC,GAAEA,IAAEF,EAAC;AAAE,MAAAI,MAAGA,GAAEH,IAAEE,IAAEH,EAAC;AAAA,IAAC;AAAA,EAAC,EAAE,IAAGA,KAAE,GAAGA,EAAC,OAAI,SAASA,IAAEC,IAAE;AAAC,OAAGD,IAAEA,GAAE,EAAE,IAAI,CAAC;AAAE,aAAQE,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,IAAGF,IAAE,EAAEC,GAAEC,EAAC,CAAC,KAAG,IAAI,WAAW,CAAC,CAAC;AAAA,EAAC,GAAED,IAAED,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,QAAIC,KAAE,IAAED,GAAE,CAAC;AAAE,QAAG,IAAEC,GAAE,QAAOD;AAAE,aAAQE,KAAE,GAAEC,KAAE,GAAED,KAAEF,GAAE,QAAOE,MAAI;AAAC,YAAMD,KAAEF,GAAEC,GAAEE,EAAC,CAAC;AAAE,cAAMD,OAAID,GAAEG,IAAG,IAAEF;AAAA,IAAE;AAAC,WAAOE,KAAED,OAAIF,GAAE,SAAOG,KAAG,GAAGH,IAAE,SAAO,IAAEC,GAAE,GAAE,IAAEA,MAAG,OAAO,OAAOD,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,IAAI,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAIA,KAAE,GAAGA,IAAE,GAAG,CAAC,QAAO,MAAM,GAAEG,IAAE,IAAE,GAAEC,EAAC,GAAE,GAAGD,KAAE,IAAEF,GAAE,CAAC,CAAC,IAAGG,KAAE,GAAGH,IAAEE,IAAED,EAAC,cAAa,KAAG,MAAI,IAAEE,GAAE,OAAKA,KAAEA,GAAE,GAAG,GAAG,KAAKJ,EAAC,GAAE,GAAGC,IAAEE,IAAED,IAAEE,EAAC,KAAGA,GAAE,GAAGJ,EAAC,IAAE,MAAM,QAAQI,EAAC,KAAG,KAAG,IAAEA,GAAE,CAAC,MAAI,GAAGH,IAAEE,IAAED,IAAEE,KAAE,GAAGA,EAAC,CAAC,GAAEA,GAAE,KAAKJ,EAAC,KAAG,GAAGC,IAAEE,IAAED,IAAE,CAACF,EAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGH,cAAa,GAAG,CAAAA,GAAE,SAAS,CAACA,IAAEI,OAAI;AAAC,OAAGL,IAAEE,IAAE,GAAG,CAACG,IAAEJ,EAAC,GAAEE,IAAE,KAAE,GAAEC,EAAC;AAAA,EAAC,EAAE;AAAA,WAAU,MAAM,QAAQH,EAAC,EAAE,UAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAI;AAAC,UAAMG,KAAEP,GAAEI,EAAC;AAAE,UAAM,QAAQG,EAAC,KAAG,GAAGR,IAAEE,IAAE,GAAGM,IAAEL,IAAE,KAAE,GAAEC,EAAC;AAAA,EAAC;AAAC,EAAE;AAAE,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,MAAGD,MAAE,SAASD,IAAE;AAAC,QAAG,QAAMA,GAAE,QAAOA;AAAE,UAAMC,KAAE,OAAOD;AAAE,QAAG,aAAWC,GAAE,QAAO,OAAO,GAAG,IAAGD,EAAC,CAAC;AAAE,QAAG,GAAGA,EAAC,GAAE;AAAC,UAAG,aAAWC,GAAE,QAAO,GAAGD,EAAC;AAAE,UAAG,aAAWC,GAAE,QAAO,GAAGD,EAAC;AAAA,IAAC;AAAA,EAAC,GAAEC,EAAC,GAAE,QAAMA,IAAE;AAAC,QAAG,YAAU,OAAOA,GAAE,IAAGA,EAAC;AAAE,QAAG,QAAMA,GAAE,SAAO,GAAGD,IAAEE,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,MAAC,KAAI;AAAS,QAAAD,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,IAAG,EAAE;AAAE;AAAA,MAAM,KAAI;AAAS,QAAAE,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGF,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,QAAMA,OAAI,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,GAAE,EAAE,EAAE,KAAKC,KAAE,IAAE,CAAC;AAAE;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,GAAGD,IAAEE,IAAE,EAAED,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,KAAGJ,IAAEE,IAAE,GAAGD,IAAEE,EAAC,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,UAAMD,MAAG,YAAU,OAAOA,MAAGA,cAAa,MAAI,EAAEA,EAAC,IAAE,EAAEA,EAAC,KAAG,EAAE,CAAC,IAAEA,KAAE,SAAQ,QAAMA,MAAG,GAAGD,IAAEE,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG;AAAC,IAAI,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAIF,GAAE,EAAE,QAAM;AAAG,MAAIG,KAAEH,GAAE;AAAE,EAAAA,KAAE,GAAGG,EAAC;AAAE,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAA,KAAE,KAAGC,MAAG,MAAI;AAAE,QAAMC,KAAED,OAAI,KAAG;AAAK,SAAOJ,KAAE,cAAY,UAAQI,MAAGJ,IAAE,GAAGC,IAAEC,IAAE,QAAMG,KAAEL,KAAE,MAAIG,MAAG,IAAE,KAAG,KAAGE,KAAE,SAAOF,KAAEH,KAAEG,KAAE,KAAK,IAAI,GAAEE,KAAE,IAAI,KAAGL,KAAE,iBAAiB,GAAE;AAAE,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,KAAEA,GAAE,IAAGE,KAAE,OAAK,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC,GAAG,WAAW,GAAE,CAACD,IAAE,IAAE,GAAE,KAAGC,GAAE,UAAU,GAAE,IAAE,GAAE,KAAGA,GAAE,UAAU,GAAE,IAAE,GAAE,GAAGF,IAAE,EAAE,GAAE,GAAGA,IAAE,EAAE;AAAE,IAAG,GAAG,CAAC;AAApa,IAAsa,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAK,GAAGD,IAAEE,IAAE,CAAC,GAAEF,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,EAAE;AAAE,IAAG,EAAE;AAA3iB,IAA6iB,KAAG,GAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,GAAG,UAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,QAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,YAAMH,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAED,KAAEA,GAAE,GAAE,GAAGE,EAAC,GAAE,GAAGF,IAAE,EAAE;AAAA,EAAE;AAAC,IAAG,EAAE;AAA3rB,IAA6rB,KAAG,GAAG,KAAI,SAASH,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,OAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAE,IAAEC,GAAE,MAAM;AAAE,aAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,KAAEF,GAAE,GAAE,GAAGC,GAAEE,EAAC,CAAC,GAAE,GAAGD,IAAE,EAAE;AAAA,EAAC;AAAC,IAAG,EAAE;AAA70B,IAA+0B,KAAG,IAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAAr5B,IAAu5B,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,GAAE,EAAE,KAAG,SAAOA,EAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAA9+B,IAAg/B,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,GAAE,EAAE,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAGA,EAAC,IAAG;AAAC,QAAG,YAAU,OAAOA,GAAE,IAAGA,EAAC;AAAE,QAAG,QAAMA,GAAE,SAAO,GAAGD,IAAEE,IAAE,CAAC,GAAE,OAAOD,IAAE;AAAA,MAAC,KAAI;AAAS,QAAAD,KAAEA,GAAE,GAAE,GAAGC,EAAC,GAAE,GAAGD,IAAE,IAAG,EAAE;AAAE;AAAA,MAAM,KAAI;AAAS,QAAAE,KAAE,OAAO,QAAQ,IAAGD,EAAC,GAAEC,KAAE,IAAI,GAAG,OAAOA,KAAE,OAAO,UAAU,CAAC,GAAE,OAAOA,MAAG,OAAO,EAAE,CAAC,CAAC,GAAE,GAAGF,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAE;AAAA,MAAM;AAAQ,QAAAA,KAAE,GAAGD,EAAC,GAAE,GAAGD,GAAE,GAAEE,GAAE,GAAEA,GAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,IAAG,GAAG,CAAC;AAAl2C,IAAo2C,KAAG,IAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAAv6C,IAAy6C,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGF,IAAEE,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,IAAGF,GAAE,GAAEC,GAAEC,EAAC,CAAC;AAAE,OAAGF,IAAEE,EAAC;AAAA,EAAC;AAAC,IAAG,EAAE;AAAxoD,IAA0oD,KAAG,IAAI,SAASF,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAA9tD,IAAguD,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAAnyD,IAAqyD,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,WAAMF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAA13D,IAA43D,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAIA,KAAE,GAAGA,EAAC,GAAE,GAAGC,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,EAAE,KAAKF,EAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,GAAG,UAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,QAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,YAAMH,MAAG,GAAGF,IAAEC,IAAE,EAAEC,EAAC,CAAC;AAAA,EAAC;AAAC,IAAG,EAAE;AAA7jE,IAA+jE,KAAG,IAAI,SAASL,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,QAAMF,KAAE,GAAGA,EAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAAlpE,IAAopE,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,EAAC,CAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAArtE,IAAutE,MAAG,SAASA,IAAEC,IAAEC,KAAE,IAAG;AAAC,SAAO,IAAI,GAAGF,IAAEC,IAAEC,EAAC;AAAC,IAAG,SAASF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIJ,GAAE,MAAIG,KAAE,GAAG,QAAOA,IAAE,IAAE,GAAE,GAAGF,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,IAAE,EAAE,KAAKC,EAAC,GAAE,GAAGH,IAAEG,IAAEC,EAAC,GAAE;AAAG,KAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,MAAM,QAAQH,EAAC,EAAE,UAAQI,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,KAAI,IAAGL,IAAEC,GAAEI,EAAC,GAAEH,IAAEC,IAAEC,EAAC;AAAC,EAAE;AAA37E,IAA67E,KAAG,IAAI,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIL,GAAE,MAAI,GAAGC,IAAE,IAAEA,GAAE,CAAC,GAAEI,IAAEH,EAAC,GAAE,GAAGF,IAAEC,KAAE,GAAGA,IAAEE,IAAED,EAAC,GAAEE,EAAC,GAAE;AAAG,IAAG,EAAE;AAAxhF,IAA0hF,KAAG,IAAI,SAASJ,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,EAAC,CAAC,GAAE;AAAG,IAAG,IAAG,EAAE;AAA3lF,IAA6lF,KAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,GAAG,UAAQO,KAAE,GAAEA,KAAEP,GAAE,QAAOO,MAAI;AAAC,QAAIL,KAAEH,IAAEI,KAAEF,IAAEG,KAAEJ,GAAEO,EAAC;AAAE,YAAMH,OAAI,GAAGF,IAAEC,IAAE,CAAC,GAAE,GAAGD,GAAE,GAAEE,EAAC;AAAA,EAAE;AAAC,IAAG,EAAE;AAAr0F,IAAu0F,KAAG,IAAI,SAASL,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,OAAKF,KAAE,GAAGA,GAAE,CAAC,KAAG,SAAOA,EAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,MAAI,QAAMA,OAAI,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE,IAAG,EAAE;AAA39F,IAA69F,KAAG,IAAI,SAASD,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,IAAE,GAAGF,GAAE,CAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,WAAOD,KAAE,GAAGA,EAAC,OAAKA,KAAE,SAASA,IAAE,EAAE,GAAE,GAAGD,IAAEE,IAAE,CAAC,GAAE,GAAGF,GAAE,GAAEC,EAAC;AAAE,IAAG,EAAE;AAAE,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,SAAK,IAAED,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAE,IAAG,KAAK,IAAE,IAAG,KAAK,eAAa;AAAA,EAAM;AAAA,EAAC,WAAU;AAAC,MAAE,IAAI;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAO,IAAI,GAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,SAAM,CAACC,IAAEC,OAAI;AAAC,QAAG,GAAG,QAAO;AAAC,YAAMH,KAAE,GAAG,IAAI;AAAE,MAAAA,GAAE,EAAEG,EAAC,GAAE,GAAGH,GAAE,GAAEE,IAAEC,EAAC,GAAED,KAAEF;AAAA,IAAC,MAAM,CAAAE,KAAE,IAAI,MAAK;AAAA,MAAC,YAAYF,IAAEC,IAAE;AAAC,YAAG,GAAG,QAAO;AAAC,gBAAMC,KAAE,GAAG,IAAI;AAAE,aAAGA,IAAEF,IAAEC,EAAC,GAAED,KAAEE;AAAA,QAAC,MAAM,CAAAF,KAAE,IAAI,MAAK;AAAA,UAAC,YAAYA,IAAEC,IAAE;AAAC,iBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,GAAG,MAAKD,IAAEC,EAAC;AAAA,UAAC;AAAA,UAAC,QAAO;AAAC,iBAAK,IAAE,MAAK,KAAK,IAAE,OAAG,KAAK,IAAE,KAAK,IAAE,KAAK,IAAE,GAAE,KAAK,KAAG;AAAA,UAAE;AAAA,QAAC,EAAED,IAAEC,EAAC;AAAE,aAAK,IAAED,IAAE,KAAK,IAAE,KAAK,EAAE,GAAE,KAAK,IAAE,KAAK,IAAE,IAAG,KAAK,EAAEC,EAAC;AAAA,MAAC;AAAA,MAAC,EAAE,EAAC,IAAGD,KAAE,MAAE,IAAE,CAAC,GAAE;AAAC,aAAK,KAAGA;AAAA,MAAC;AAAA,IAAC,EAAEE,IAAEC,EAAC;AAAE,QAAG;AAAC,YAAMA,KAAE,IAAIH,MAAEK,KAAEF,GAAE;AAAE,SAAGF,EAAC,EAAEI,IAAEH,EAAC;AAAE,UAAIE,KAAED;AAAA,IAAC,UAAC;AAAQ,MAAAD,GAAE,EAAE,MAAM,GAAEA,GAAE,IAAE,IAAGA,GAAE,IAAE,IAAG,GAAG,SAAO,OAAK,GAAG,KAAKA,EAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAE;AAAC,SAAO,WAAU;AAAC,UAAMC,KAAE,IAAI,MAAK;AAAA,MAAC,cAAa;AAAC,aAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,IAAE,IAAI,MAAK;AAAA,UAAC,cAAa;AAAC,iBAAK,IAAE,CAAC;AAAA,UAAC;AAAA,UAAC,SAAQ;AAAC,mBAAO,KAAK,EAAE;AAAA,UAAM;AAAA,UAAC,MAAK;AAAC,kBAAMD,KAAE,KAAK;AAAE,mBAAO,KAAK,IAAE,CAAC,GAAEA;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAE,OAAG,KAAK,GAAEC,IAAE,GAAG,IAAG,IAAG,IAAGD,EAAC,CAAC,GAAE,GAAGC,IAAEA,GAAE,EAAE,IAAI,CAAC;AAAE,UAAMC,KAAE,IAAI,WAAWD,GAAE,CAAC,GAAEE,KAAEF,GAAE,GAAEG,KAAED,GAAE;AAAO,QAAIE,KAAE;AAAE,aAAQL,KAAE,GAAEA,KAAEI,IAAEJ,MAAI;AAAC,YAAMC,KAAEE,GAAEH,EAAC;AAAE,MAAAE,GAAE,IAAID,IAAEI,EAAC,GAAEA,MAAGJ,GAAE;AAAA,IAAM;AAAC,WAAOA,GAAE,IAAE,CAACC,EAAC,GAAEA;AAAA,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,SAAO,MAAIF,GAAE,MAAI,GAAGC,IAAEC,KAAGF,KAAE,GAAGA,EAAC,OAAK,EAAE,IAAE,SAAOA,EAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,QAAMD,IAAE;AAAC,QAAGA,cAAa,IAAG;AAAC,YAAME,KAAEF,GAAE;AAAG,aAAO,MAAKE,OAAIF,KAAEE,GAAEF,EAAC,GAAE,QAAMA,MAAG,GAAGD,IAAEE,IAAE,GAAGD,EAAC,EAAE,MAAM;AAAA,IAAG;AAAC,QAAG,MAAM,QAAQA,EAAC,EAAE;AAAA,EAAM;AAAC,KAAGD,IAAEC,IAAEC,EAAC;AAAC,IAAG,EAAE,CAAC;AAAE,IAAI;AAAJ,IAAO,KAAG,WAAW;AAAa,SAAS,GAAGF,IAAE;AAAC,aAAS,OAAK,MAAG,WAAU;AAAC,QAAIA,KAAE;AAAK,QAAG,CAAC,GAAG,QAAOA;AAAE,QAAG;AAAC,YAAMC,KAAE,CAAAD,OAAGA;AAAE,MAAAA,KAAE,GAAG,aAAa,aAAY,EAAC,YAAWC,IAAE,cAAaA,IAAE,iBAAgBA,GAAC,CAAC;AAAA,IAAC,SAAOD,IAAE;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC,GAAE;AAAG,MAAIC,KAAE;AAAG,SAAO,IAAI,MAAK;AAAA,IAAC,YAAYD,IAAE;AAAC,WAAK,IAAEA;AAAA,IAAC;AAAA,IAAC,WAAU;AAAC,aAAO,KAAK,IAAE;AAAA,IAAE;AAAA,EAAC,EAAEC,KAAEA,GAAE,gBAAgBD,EAAC,IAAEA,EAAC;AAAC;AAAC,SAAS,GAAGA,OAAKC,IAAE;AAAC,MAAG,MAAIA,GAAE,OAAO,QAAO,GAAGD,GAAE,CAAC,CAAC;AAAE,MAAIE,KAAEF,GAAE,CAAC;AAAE,WAAQG,KAAE,GAAEA,KAAEF,GAAE,QAAOE,KAAI,CAAAD,MAAG,mBAAmBD,GAAEE,EAAC,CAAC,IAAEH,GAAEG,KAAE,CAAC;AAAE,SAAO,GAAGD,EAAC;AAAC;AAAC,IAAI,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAA9B,IAAgC,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5E,IAA8E,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAO,MAAIF,GAAE,KAAG,MAAIA,GAAE,OAAKC,KAAE,GAAGA,IAAE,IAAEA,GAAE,CAAC,GAAEC,IAAE,KAAE,GAAE,KAAGF,GAAE,IAAE,GAAGA,IAAE,IAAGC,EAAC,IAAEA,GAAE,KAAK,GAAGD,GAAE,CAAC,CAAC,GAAE;AAAG,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,MAAG,SAAOD,KAAE,GAAG,IAAGA,EAAC,MAAIA,GAAE,QAAO;AAAC,IAAAC,KAAE,GAAGF,IAAEE,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,IAAGF,GAAE,GAAEC,GAAEC,EAAC,CAAC;AAAE,OAAGF,IAAEE,EAAC;AAAA,EAAC;AAAC,IAAG,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,EAAE;AAAzV,IAA2V,KAAG,CAAC,GAAE,IAAG,EAAE;AAAtW,IAAwW,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAApZ,IAAsZ,KAAG,CAAC,CAAC;AAA3Z,IAA6Z,KAAG,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE;AAAhb,IAAkb,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,IAAE,CAAC;AAAA,EAAC;AAAC;AAAhe,IAAke,KAAG,CAAC;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,EAAE;AAAE,IAAI,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAE,GAAGC,EAAC,GAAE,EAAE;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAEC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAC;AAA/E,IAAiF,KAAG,CAAC,IAAG,CAAC,CAAC;AAA1F,IAA4F,KAAG,CAAC,GAAE,IAAG,GAAE,EAAE;AAAzG,IAA2G,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,IAAE,GAAE,IAAGC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,IAAGC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,KAAGD,IAAE,IAAGC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMA,IAAE,GAAG;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,MAAK,GAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAlF,IAAoF,KAAG,CAAC,MAAK,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,IAAG,CAAC,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,CAAC,IAAG,CAAC,CAAC,GAAE,KAAI,EAAE,GAAE,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,CAAC,IAAG,CAAC,GAAE,EAAE,GAAE,KAAI,IAAG,EAAE,GAAE,IAAG,IAAG,CAAC,MAAK,IAAG,IAAG,IAAG,KAAI,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,KAAI,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,EAAE;AAAE,IAAI,KAAG,GAAG,IAAG,EAAE;AAAf,IAAiB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA7D,IAA+D,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAApI,IAAsI,KAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC;AAA9J,IAAgK,KAAG,GAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzN,IAA2N,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvQ,IAAyQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAC;AAAvW,IAAyW,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,EAAE,CAAC;AAA3gB,IAA6gB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzjB,IAA2jB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA5nB,IAA8nB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1qB,IAA4qB,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,CAAC;AAA7uB,IAA+uB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3xB,IAA6xB,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA9yB,IAAgzB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3H,IAA6H,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzK,IAA2K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,UAAMA,KAAE,GAAG,IAAI;AAAE,WAAO,QAAMA,KAAE,EAAE,IAAEA;AAAA,EAAC;AAAC;AAAlQ,IAAoQ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhT,IAAkT,KAAG,CAAC,GAAE,CAAC;AAAzT,IAA2T,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,EAAE,GAAE,EAAE,CAAC;AAAtZ,IAAwZ,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAApc,IAAsc,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAA1d,IAA4d,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxgB,IAA0gB,KAAG,CAAC,GAAE,IAAG,EAAE;AAArhB,IAAuhB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAnkB,IAAqkB,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,CAAC;AAAllB,IAAolB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,QAAM,GAAG,MAAK,CAAC;AAAA,EAAC;AAAC;AAAtrB,IAAwrB,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,GAAG,MAAK,CAAC,CAAC,KAAG;AAAA,EAAE;AAAC;AAAlwB,IAAowB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,CAAC;AAAtyB,IAAwyB,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE;AAA31B,IAA61B,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAz4B,IAA24B,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA55B,IAA85B,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC,GAAE,CAAC,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,EAAE,CAAC;AAA3E,IAA6E,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzH,IAA2H,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvK,IAAyK,KAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,EAAE;AAA9L,IAAgM,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAA5N,IAA8N,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,MAAK,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,OAAG,MAAK,CAAC;AAAA,EAAC;AAAC;AAAxF,IAA0F,KAAG,CAAC,GAAE,IAAG,EAAE;AAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1L,IAA4L,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAxO,IAA0O,KAAG,CAAC,GAAE,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,EAAE;AAA/P,IAAiQ,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA/Q,IAAiR,KAAG,CAAC,GAAE,IAAG,EAAE;AAA5R,IAA8R,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA/S,IAAiT,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,IAAG,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA5I,IAA8I,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA1L,IAA4L,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE;AAA7M,IAA+M,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,IAAG,CAAC,GAAE,EAAE,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA9F,IAAgG,KAAG,CAAC,GAAE,IAAG,EAAE;AAA3G,IAA6G,KAAG,GAAG,WAAU,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIA,KAAE,KAAK;AAAE,UAAMC,KAAE,IAAED,GAAE,CAAC,GAAEE,KAAE,IAAED;AAAE,WAAOD,MAAE,SAASA,IAAEC,IAAEC,IAAE;AAAC,UAAIC,KAAE;AAAG,YAAMC,KAAE,IAAEH;AAAE,UAAII,KAAE;AAAG,UAAG,QAAMH,IAAE;AAAC,YAAGE,GAAE,QAAO,GAAG;AAAE,QAAAF,KAAE,CAAC;AAAA,MAAC,WAASA,GAAE,gBAAc,IAAG;AAAC,YAAG,MAAI,IAAEA,GAAE,MAAIE,GAAE,QAAOF;AAAE,QAAAA,KAAEA,GAAE,GAAG;AAAA,MAAC,MAAM,OAAM,QAAQA,EAAC,IAAEG,KAAE,CAAC,EAAE,KAAG,IAAEH,GAAE,CAAC,MAAIA,KAAE,CAAC;AAAE,UAAGE,IAAE;AAAC,YAAG,CAACF,GAAE,OAAO,QAAO,GAAG;AAAE,QAAAG,OAAIA,KAAE,MAAG,GAAGH,EAAC;AAAA,MAAE,MAAM,CAAAG,OAAIA,KAAE,OAAGH,KAAE,GAAGA,EAAC;AAAG,aAAOG,OAAI,MAAI,IAAEH,GAAE,CAAC,KAAGA,GAAE,CAAC,KAAG,MAAI,KAAGD,MAAG,GAAGC,IAAE,EAAE,IAAG,GAAGF,IAAEC,IAAE,GAAEE,KAAE,IAAI,GAAGD,IAAEC,IAAE,IAAG,MAAM,CAAC,GAAEA;AAAA,IAAC,GAAEH,IAAEC,IAAE,GAAGD,IAAEC,IAAE,CAAC,CAAC,GAAE,CAACC,MAAG,OAAKF,GAAE,KAAG,OAAIA;AAAA,EAAC;AAAC,CAAC;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,CAAC,MAAG,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,CAAC,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,EAAE;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,GAAG,WAAU,EAAE;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASD,GAAE,qBAAmB,GAAGC,IAAE,GAAE,GAAGD,GAAE,kBAAkB,CAAC,IAAE,WAASA,GAAE,sBAAoB,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,aAAW,GAAGC,IAAE,GAAED,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,iBAAe,GAAGC,IAAE,GAAED,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,oBAAkB,GAAGC,IAAE,GAAED,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAGC,IAAE,CAAC,GAAE,WAASD,GAAE,mBAAiB,GAAGC,IAAE,GAAED,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAGC,IAAE,CAAC,GAAEA;AAAC;AAAC,SAAS,GAAGD,IAAEC,KAAE,IAAGC,KAAE,IAAG;AAAC,SAAM,EAAC,YAAWF,GAAE,KAAK,CAAAA,QAAI,EAAC,OAAM,GAAGA,IAAE,CAAC,KAAG,KAAG,IAAG,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,cAAa,GAAGA,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,GAAG,GAAE,WAAUC,IAAE,UAASC,GAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEE,KAAE,GAAGF,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEG,KAAE,GAAGH,IAAE,GAAE,IAAG,GAAG,CAAC,GAAEI,KAAE,GAAGJ,IAAE,GAAE,IAAG,GAAG,CAAC;AAAE,QAAMK,KAAE,EAAC,YAAW,CAAC,GAAE,WAAU,CAAC,EAAC;AAAE,WAAQL,KAAE,GAAEA,KAAEC,GAAE,QAAOD,KAAI,CAAAK,GAAE,WAAW,KAAK,EAAC,OAAMJ,GAAED,EAAC,GAAE,OAAME,GAAEF,EAAC,KAAG,IAAG,cAAaG,GAAEH,EAAC,KAAG,IAAG,aAAYI,GAAEJ,EAAC,KAAG,GAAE,CAAC;AAAE,OAAIC,KAAE,GAAGD,IAAE,IAAG,CAAC,GAAG,EAAE,OAAKK,GAAE,cAAY,EAAC,SAAQ,GAAGJ,IAAE,CAAC,KAAG,GAAE,SAAQ,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,QAAO,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,EAAC,IAAG,GAAGD,IAAE,IAAG,CAAC,GAAG,EAAE,EAAE,OAAO,YAAUC,MAAK,GAAGD,IAAE,IAAG,CAAC,EAAE,EAAE,EAAE,CAAAK,GAAE,UAAU,KAAK,EAAC,GAAE,GAAGJ,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,GAAE,CAAC;AAAE,SAAOI;AAAC;AAAC,SAAS,GAAGL,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGF,IAAE,IAAG,CAAC,EAAE,CAAAC,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUC,MAAK,GAAGF,IAAE,IAAG,CAAC,EAAE,CAAAC,GAAE,KAAK,EAAC,GAAE,GAAGC,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,SAAO,MAAM,KAAKA,KAAG,CAAAA,OAAGA,KAAE,MAAIA,KAAE,MAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,MAAGD,GAAE,WAASC,GAAE,OAAO,OAAM,MAAM,2EAA2ED,GAAE,MAAM,QAAQC,GAAE,MAAM,IAAI;AAAE,MAAIC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEL,GAAE,QAAOK,KAAI,CAAAH,MAAGF,GAAEK,EAAC,IAAEJ,GAAEI,EAAC,GAAEF,MAAGH,GAAEK,EAAC,IAAEL,GAAEK,EAAC,GAAED,MAAGH,GAAEI,EAAC,IAAEJ,GAAEI,EAAC;AAAE,MAAGF,MAAG,KAAGC,MAAG,EAAE,OAAM,MAAM,4DAA4D;AAAE,SAAOF,KAAE,KAAK,KAAKC,KAAEC,EAAC;AAAC;AAAC,IAAI;AAAG,GAAG,SAAS,IAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,GAAG,SAAS,IAAE;AAAG,IAAM,KAAG,IAAI,WAAW,CAAC,GAAE,IAAG,KAAI,KAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,KAAI,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,KAAI,IAAG,KAAI,IAAG,EAAE,CAAC;AAAE,eAAe,KAAI;AAAC,MAAG,WAAS,GAAG,KAAG;AAAC,UAAM,YAAY,YAAY,EAAE,GAAE,KAAG;AAAA,EAAE,QAAM;AAAC,SAAG;AAAA,EAAE;AAAC,SAAO;AAAE;AAAC,eAAe,GAAGJ,IAAEC,KAAE,MAAK;AAAC,QAAMC,KAAE,MAAM,GAAG,IAAE,kBAAgB;AAAuB,SAAM,EAAC,gBAAe,GAAGD,EAAC,IAAID,EAAC,IAAIE,EAAC,OAAM,gBAAe,GAAGD,EAAC,IAAID,EAAC,IAAIE,EAAC,QAAO;AAAC;AAAC,IAAI,KAAG,MAAK;AAAC;AAAE,SAAS,KAAI;AAAC,MAAIF,KAAE;AAAU,SAAM,eAAa,OAAO,oBAAkB,EAAC,SAASA,KAAE,WAAU;AAAC,YAAOA,KAAEA,GAAE,WAAW,SAAS,QAAQ,KAAG,CAACA,GAAE,SAAS,QAAQ;AAAA,EAAC,GAAEA,EAAC,KAAG,CAAC,GAAGA,KAAEA,GAAE,UAAU,MAAM,0BAA0B,MAAIA,GAAE,UAAQ,KAAG,OAAOA,GAAE,CAAC,CAAC,KAAG;AAAI;AAAC,eAAe,GAAGA,IAAE;AAAC,MAAG,cAAY,OAAO,eAAc;AAAC,UAAMC,KAAE,SAAS,cAAc,QAAQ;AAAE,WAAOA,GAAE,MAAID,GAAE,SAAS,GAAEC,GAAE,cAAY,aAAY,IAAI,SAAS,CAACD,IAAEE,OAAI;AAAC,MAAAD,GAAE,iBAAiB,SAAQ,MAAI;AAAC,QAAAD,GAAE;AAAA,MAAC,IAAG,KAAE,GAAEC,GAAE,iBAAiB,UAAS,CAAAD,OAAG;AAAC,QAAAE,GAAEF,EAAC;AAAA,MAAC,IAAG,KAAE,GAAE,SAAS,KAAK,YAAYC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAC,gBAAcD,GAAE,SAAS,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA,GAAE,aAAW,CAACA,GAAE,YAAWA,GAAE,WAAW,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,WAASA,GAAE,eAAa,CAACA,GAAE,cAAaA,GAAE,aAAa,IAAE,CAACA,GAAE,OAAMA,GAAE,MAAM;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,KAAG,QAAQ,MAAM,mHAAmH,GAAEE,GAAED,KAAED,GAAE,EAAE,gBAAgBC,EAAC,CAAC,GAAED,GAAE,EAAE,MAAMC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,EAAE,OAAO,OAAM,MAAM,8BAA8B;AAAE,MAAGE,KAAEF,GAAE,EAAE,qBAAqBE,EAAC,IAAEF,GAAE,EAAE,qBAAqB,GAAE,EAAEE,KAAEF,GAAE,EAAE,OAAO,WAAW,QAAQ,KAAGA,GAAE,EAAE,OAAO,WAAW,OAAO,GAAG,OAAM,MAAM,0HAA0H;AAAE,EAAAA,GAAE,EAAE,uCAAqCE,GAAE,YAAYA,GAAE,qBAAoB,IAAE,GAAEA,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcD,EAAC,GAAED,GAAE,EAAE,uCAAqCE,GAAE,YAAYA,GAAE,qBAAoB,KAAE;AAAE,QAAK,CAACC,IAAEC,EAAC,IAAE,GAAGH,EAAC;AAAE,SAAM,CAACD,GAAE,KAAGG,OAAIH,GAAE,EAAE,OAAO,SAAOI,OAAIJ,GAAE,EAAE,OAAO,WAASA,GAAE,EAAE,OAAO,QAAMG,IAAEH,GAAE,EAAE,OAAO,SAAOI,KAAG,CAACD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGJ,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,KAAG,QAAQ,MAAM,mHAAmH;AAAE,QAAMG,KAAE,IAAI,YAAYF,GAAE,MAAM;AAAE,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAC,GAAED,EAAC,IAAEF,GAAE,EAAE,gBAAgBC,GAAEC,EAAC,CAAC;AAAE,EAAAD,KAAED,GAAE,EAAE,QAAQ,IAAEG,GAAE,MAAM,GAAEH,GAAE,EAAE,QAAQ,IAAIG,IAAEF,MAAG,CAAC,GAAEC,GAAED,EAAC;AAAE,aAAUA,MAAKE,GAAE,CAAAH,GAAE,EAAE,MAAMC,EAAC;AAAE,EAAAD,GAAE,EAAE,MAAMC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBC,EAAC,IAAEC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,CAAC;AAAE,EAAAH,GAAE,EAAE,kBAAgBA,GAAE,EAAE,mBAAiB,CAAC,GAAEA,GAAE,EAAE,gBAAgBC,EAAC,IAAE,CAACD,IAAEC,IAAEG,OAAI;AAAC,IAAAH,MAAGC,GAAEC,IAAEC,EAAC,GAAED,KAAE,CAAC,KAAGA,GAAE,KAAKH,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,iBAAe,SAASA,IAAE;AAAC,SAAO,GAAG,UAASA,EAAC;AAAC,GAAE,GAAG,eAAa,SAASA,IAAE;AAAC,SAAO,GAAG,QAAOA,EAAC;AAAC,GAAE,GAAG,4BAA0B,SAASA,IAAE;AAAC,SAAO,GAAG,sBAAqBA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,gBAAc,SAASA,IAAE;AAAC,SAAO,GAAG,SAAQA,EAAC;AAAC,GAAE,GAAG,kBAAgB,WAAU;AAAC,SAAO,GAAG;AAAC;AAAE,eAAe,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAOH,KAAE,OAAM,OAAMA,IAAEC,IAAEC,IAAEC,IAAEC,OAAI;AAAC,QAAGH,MAAG,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK,cAAc,OAAM,MAAM,wBAAwB;AAAE,QAAGC,OAAI,MAAM,GAAGA,EAAC,GAAE,CAAC,KAAK,eAAe,OAAM,MAAM,wBAAwB;AAAE,WAAO,KAAK,UAAQE,QAAKH,KAAE,KAAK,QAAQ,aAAWG,GAAE,YAAWA,GAAE,wBAAsBH,GAAE,sBAAoBG,GAAE,uBAAsBA,KAAE,MAAM,KAAK,cAAc,KAAK,UAAQA,EAAC,GAAE,KAAK,gBAAc,KAAK,SAAO,QAAO,IAAIJ,GAAEI,IAAED,EAAC;AAAA,EAAC,GAAGH,IAAEE,GAAE,gBAAeA,GAAE,iBAAgBD,IAAE,EAAC,YAAW,CAAAD,OAAGA,GAAE,SAAS,OAAO,IAAEE,GAAE,eAAe,SAAS,IAAEA,GAAE,mBAAiBF,GAAE,SAAS,OAAO,IAAEE,GAAE,gBAAgB,SAAS,IAAEF,GAAC,CAAC,GAAE,MAAMA,GAAE,EAAEG,EAAC,GAAEH;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGF,GAAE,aAAY,IAAG,CAAC,KAAG,IAAI;AAAG,cAAU,OAAOC,MAAG,GAAGC,IAAE,GAAE,GAAGD,EAAC,CAAC,GAAE,GAAGC,IAAE,CAAC,KAAGD,cAAa,eAAa,GAAGC,IAAE,GAAE,GAAGD,IAAE,KAAE,CAAC,GAAE,GAAGC,IAAE,CAAC,IAAG,GAAGF,GAAE,aAAY,GAAE,GAAEE,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAED,GAAE,EAAE;AAAO,QAAG,MAAIC,GAAE,OAAM,MAAMD,GAAE,EAAE,CAAC,EAAE,OAAO;AAAE,QAAGC,KAAE,EAAE,OAAM,MAAM,kCAAgCD,GAAE,EAAE,KAAK,CAAAA,OAAGA,GAAE,QAAQ,EAAE,KAAK,IAAI,CAAC;AAAA,EAAC,UAAC;AAAQ,IAAAA,GAAE,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAE,KAAK,IAAIA,GAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAE;AAAC,EAAAD,GAAE,IAAE,IAAI,MAAG,GAAGA,GAAE,GAAE,uBAAuB,GAAE,GAAGA,GAAE,GAAE,aAAa,GAAE,GAAGA,GAAE,GAAE,wBAAwB,GAAE,GAAGC,IAAE,aAAa,GAAE,GAAGA,IAAED,GAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,KAAGD,GAAE,GAAEC,EAAC,GAAE,GAAGD,GAAE,GAAEC,KAAE,aAAa;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,EAAE,gBAAgB,MAAG,eAAcA,GAAE,CAAC;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,IAAEA,IAAE,KAAK,IAAE,CAAC,GAAE,KAAK,IAAE,GAAE,KAAK,EAAE,sBAAsB,KAAE;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,KAAE,MAAG;AAAC,QAAGA,IAAE;AAAC,YAAMA,KAAED,GAAE,eAAa,CAAC;AAAE,UAAGA,GAAE,aAAa,oBAAkBA,GAAE,aAAa,eAAe,OAAM,MAAM,6EAA6E;AAAE,UAAG,EAAE,GAAG,KAAK,aAAY,IAAG,CAAC,GAAG,EAAE,KAAG,GAAG,KAAK,aAAY,IAAG,CAAC,GAAG,EAAE,KAAGA,GAAE,aAAa,oBAAkBA,GAAE,aAAa,gBAAgB,OAAM,MAAM,+EAA+E;AAAE,WAAG,SAASA,IAAEC,IAAE;AAAC,YAAIC,KAAE,GAAGF,GAAE,aAAY,IAAG,CAAC;AAAE,YAAG,CAACE,IAAE;AAAC,cAAIC,KAAED,KAAE,IAAI,MAAGE,KAAE,IAAI;AAAG,aAAGD,IAAE,GAAE,IAAGC,EAAC;AAAA,QAAC;AAAC,sBAAaH,OAAI,UAAQA,GAAE,YAAUA,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,MAAIF,KAAEC,IAAEC,KAAE,IAAI,MAAG,GAAGF,IAAE,GAAE,IAAGE,EAAC,KAAI,GAAGH,GAAE,aAAY,GAAE,GAAEE,EAAC;AAAA,MAAC,GAAE,MAAKD,EAAC,GAAEA,GAAE,eAAe,QAAO,MAAMA,GAAE,eAAe,SAAS,CAAC,EAAE,MAAM,CAAAD,OAAG;AAAC,YAAGA,GAAE,GAAG,QAAOA,GAAE,YAAY;AAAE,cAAM,MAAM,0BAA0BC,GAAE,cAAc,KAAKD,GAAE,MAAM,GAAG;AAAA,MAAC,EAAE,EAAE,MAAM,CAAAA,OAAG;AAAC,YAAG;AAAC,eAAK,EAAE,EAAE,UAAU,YAAY;AAAA,QAAC,QAAM;AAAA,QAAC;AAAC,aAAK,EAAE,EAAE,kBAAkB,KAAI,aAAY,IAAI,WAAWA,EAAC,GAAE,MAAG,OAAG,KAAE,GAAE,GAAG,MAAK,YAAY,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,MAAC,EAAE;AAAE,UAAGC,GAAE,4BAA4B,WAAW,IAAG,MAAKA,GAAE,gBAAgB;AAAA,eAAUA,GAAE,iBAAiB,SAAO,eAAeD,IAAE;AAAC,cAAMC,KAAE,CAAC;AAAE,iBAAQC,KAAE,OAAI;AAAC,gBAAK,EAAC,MAAKC,IAAE,OAAMC,GAAC,IAAE,MAAMJ,GAAE,KAAK;AAAE,cAAGG,GAAE;AAAM,UAAAF,GAAE,KAAKG,EAAC,GAAEF,MAAGE,GAAE;AAAA,QAAM;AAAC,YAAG,MAAIH,GAAE,OAAO,QAAO,IAAI,WAAW,CAAC;AAAE,YAAG,MAAIA,GAAE,OAAO,QAAOA,GAAE,CAAC;AAAE,QAAAD,KAAE,IAAI,WAAWE,EAAC,GAAEA,KAAE;AAAE,mBAAUC,MAAKF,GAAE,CAAAD,GAAE,IAAIG,IAAED,EAAC,GAAEA,MAAGC,GAAE;AAAO,eAAOH;AAAA,MAAC,GAAEC,GAAE,gBAAgB,EAAE,MAAM,CAAAD,OAAG;AAAC,WAAG,MAAKA,EAAC,GAAE,KAAK,EAAE,GAAE,KAAK,EAAE;AAAA,MAAC,EAAE;AAAA,IAAC;AAAC,WAAO,KAAK,EAAE,GAAE,KAAK,EAAE,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIA;AAAE,QAAG,KAAK,EAAE,IAAI,CAAAC,OAAG;AAAC,MAAAD,KAAE,GAAGC,EAAC;AAAA,IAAC,EAAE,GAAE,CAACD,GAAE,OAAM,MAAM,0CAA0C;AAAE,WAAOA;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,SAAK,EAAE,qBAAqB,CAACD,IAAEC,OAAI;AAAC,WAAK,EAAE,KAAK,MAAMA,EAAC,CAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,GAAG,GAAE,KAAK,EAAE,SAASD,IAAEC,EAAC,GAAE,KAAK,IAAE,QAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,iBAAiB,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,IAAE,QAAO,KAAK,EAAE,WAAW;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGD,IAAEC,IAAE;AAAC,MAAG,CAACD,GAAE,OAAM,MAAM,6CAA6CC,EAAC,EAAE;AAAE,SAAOD;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,IAAEH,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAK,EAAE,gBAAgB,KAAK,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC,GAAE,KAAK,EAAE,aAAa,KAAK,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE;AAAE,MAAGE,KAAE,GAAGC,GAAE,aAAaD,EAAC,GAAE,+BAA+B,GAAEC,GAAE,aAAaD,IAAED,EAAC,GAAEE,GAAE,cAAcD,EAAC,GAAE,CAACC,GAAE,mBAAmBD,IAAEC,GAAE,cAAc,EAAE,OAAM,MAAM,mCAAmCA,GAAE,iBAAiBD,EAAC,CAAC,EAAE;AAAE,SAAOC,GAAE,aAAaH,GAAE,GAAEE,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,GAAEG,KAAE,GAAGD,GAAE,kBAAkB,GAAE,+BAA+B;AAAE,EAAAA,GAAE,gBAAgBC,EAAC;AAAE,QAAMC,KAAE,GAAGF,GAAE,aAAa,GAAE,yBAAyB;AAAE,EAAAA,GAAE,WAAWA,GAAE,cAAaE,EAAC,GAAEF,GAAE,wBAAwBF,GAAE,CAAC,GAAEE,GAAE,oBAAoBF,GAAE,GAAE,GAAEE,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAa,CAAC,IAAG,IAAG,IAAG,GAAE,GAAE,GAAE,GAAE,EAAE,CAAC,GAAEA,GAAE,WAAW;AAAE,QAAMG,KAAE,GAAGH,GAAE,aAAa,GAAE,yBAAyB;AAAE,SAAOA,GAAE,WAAWA,GAAE,cAAaG,EAAC,GAAEH,GAAE,wBAAwBF,GAAE,CAAC,GAAEE,GAAE,oBAAoBF,GAAE,GAAE,GAAEE,GAAE,OAAM,OAAG,GAAE,CAAC,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,aAAaD,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEC,GAAE,WAAW,GAAEA,GAAE,WAAWA,GAAE,cAAa,IAAI,GAAEA,GAAE,gBAAgB,IAAI,GAAE,IAAI,GAAGA,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGL,IAAEC,IAAE;AAAC,MAAGD,GAAE,GAAE;AAAC,QAAGC,OAAID,GAAE,EAAE,OAAM,MAAM,2CAA2C;AAAA,EAAC,MAAM,CAAAA,GAAE,IAAEC;AAAC;AAAC,SAAS,GAAGD,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGH,IAAEC,EAAC,GAAED,GAAE,MAAIA,GAAE,EAAE,GAAEA,GAAE,EAAE,IAAGE,MAAGF,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,IAAE,IAAGE,KAAEF,GAAE,MAAIA,GAAE,MAAIA,GAAE,IAAE,GAAGA,IAAE,KAAE,IAAGE,KAAEF,GAAE,IAAGC,GAAE,WAAWD,GAAE,CAAC,GAAEE,GAAE,KAAK,GAAEF,GAAE,EAAE,GAAEA,KAAEG,GAAE,GAAED,GAAE,EAAE,gBAAgB,IAAI,GAAEF;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGF,IAAEC,EAAC,GAAED,KAAE,GAAGC,GAAE,cAAc,GAAE,0BAA0B,GAAEA,GAAE,YAAYA,GAAE,YAAWD,EAAC,GAAEC,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,gBAAeA,GAAE,aAAa,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,YAAWA,GAAE,oBAAmBC,MAAGD,GAAE,MAAM,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAED;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,KAAGF,IAAEC,EAAC,GAAED,GAAE,MAAIA,GAAE,IAAE,GAAGC,GAAE,kBAAkB,GAAE,8BAA8B,IAAGA,GAAE,gBAAgBA,GAAE,aAAYD,GAAE,CAAC,GAAEC,GAAE,qBAAqBA,GAAE,aAAYA,GAAE,mBAAkBA,GAAE,YAAWC,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,EAAAA,GAAE,GAAG,gBAAgBA,GAAE,EAAE,aAAY,IAAI;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmK;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,QAAG,KAAK,IAAE,GAAGA,GAAE,cAAc,GAAE,gCAAgC,GAAE,KAAK,IAAE,GAAG,MAAK,qKAAoKA,GAAE,aAAa,GAAE,KAAK,IAAE,GAAG,MAAK,KAAK,EAAE,GAAEA,GAAE,eAAe,GAAEA,GAAE,YAAY,KAAK,CAAC,GAAE,CAACA,GAAE,oBAAoB,KAAK,GAAEA,GAAE,WAAW,EAAE,OAAM,MAAM,iCAAiCA,GAAE,kBAAkB,KAAK,CAAC,CAAC,EAAE;AAAE,SAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,SAAS,GAAE,KAAK,IAAEA,GAAE,kBAAkB,KAAK,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,IAAG;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAG,KAAK,GAAE;AAAC,YAAMA,KAAE,KAAK;AAAE,MAAAA,GAAE,cAAc,KAAK,CAAC,GAAEA,GAAE,aAAa,KAAK,CAAC,GAAEA,GAAE,aAAa,KAAK,CAAC;AAAA,IAAC;AAAC,SAAK,KAAG,KAAK,EAAE,kBAAkB,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM,GAAE,KAAK,KAAG,KAAK,EAAE,MAAM;AAAA,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAgd;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,MAAM,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,IAAEA,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,mBAAmB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,qBAAqB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAA1jC,IAA4jC,KAAG,cAAc,GAAE;AAAA,EAAC,IAAG;AAAC,WAAM;AAAA,EAAmjB;AAAA,EAAC,IAAG;AAAC,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAE,KAAK,IAAE,GAAG,MAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,SAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,gBAAgB,GAAE,kBAAkB,GAAE,KAAK,IAAE,GAAGA,GAAE,mBAAmB,KAAK,GAAE,aAAa,GAAE,kBAAkB;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,UAAM,EAAE;AAAE,UAAMA,KAAE,KAAK;AAAE,IAAAA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC,GAAEA,GAAE,UAAU,KAAK,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,KAAK,KAAG,KAAK,EAAE,cAAc,KAAK,CAAC,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOD,GAAE,EAAE,MAAM,CAAAA,OAAGA,cAAa,WAAW;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,MAAM,CAAAA,OAAGA,cAAa,aAAa;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,MAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,aAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BC,EAAC,EAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,QAAGA,KAAE,GAAGD,IAAE,CAAC,EAAE,CAAAC,KAAE,IAAI,aAAaA,EAAC,EAAE,KAAK,CAAAD,OAAGA,KAAE,IAAI;AAAA,SAAM;AAAC,MAAAC,KAAE,IAAI,aAAaD,GAAE,QAAMA,GAAE,MAAM;AAAE,YAAMG,KAAE,GAAGH,EAAC;AAAE,UAAIE,KAAE,GAAGF,EAAC;AAAE,UAAG,GAAGE,IAAEC,IAAE,GAAGH,EAAC,CAAC,GAAE,kEAAkE,MAAM,GAAG,EAAE,SAAS,UAAU,QAAQ,KAAG,UAAU,UAAU,SAAS,KAAK,KAAG,cAAa,QAAM,gBAAe,KAAK,UAAS;AAAC,QAAAE,KAAE,IAAI,aAAaF,GAAE,QAAMA,GAAE,SAAO,CAAC,GAAEG,GAAE,WAAW,GAAE,GAAEH,GAAE,OAAMA,GAAE,QAAOG,GAAE,MAAKA,GAAE,OAAMD,EAAC;AAAE,iBAAQF,KAAE,GAAEG,KAAE,GAAEH,KAAEC,GAAE,QAAO,EAAED,IAAEG,MAAG,EAAE,CAAAF,GAAED,EAAC,IAAEE,GAAEC,EAAC;AAAA,MAAC,MAAM,CAAAA,GAAE,WAAW,GAAE,GAAEH,GAAE,OAAMA,GAAE,QAAOG,GAAE,KAAIA,GAAE,OAAMF,EAAC;AAAA,IAAC;AAAC,IAAAD,GAAE,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAGF,EAAC;AAAE,IAAAC,KAAE,GAAGD,EAAC;AAAE,UAAMG,KAAE,GAAGH,EAAC,GAAEI,KAAE,GAAGJ,EAAC;AAAE,IAAAE,GAAE,WAAWA,GAAE,YAAW,GAAEE,IAAEJ,GAAE,OAAMA,GAAE,QAAO,GAAEE,GAAE,KAAIA,GAAE,OAAMC,EAAC,GAAE,GAAGH,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG,CAACA,GAAE,OAAO,OAAM,MAAM,oGAAoG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAGA,KAAE,GAAGA,EAAC,GAAE,CAAC,GAAG,KAAGA,GAAE,aAAa,wBAAwB,KAAGA,GAAE,aAAa,0BAA0B,KAAGA,GAAE,aAAa,iBAAiB,EAAE,MAAGA,GAAE;AAAA,OAAS;AAAC,QAAG,CAACA,GAAE,aAAa,6BAA6B,EAAE,OAAM,MAAM,iEAAiE;AAAE,SAAGA,GAAE;AAAA,EAAI;AAAC,SAAO;AAAE;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAC,GAAE,SAAS,GAAE,GAAED,GAAE,OAAMA,GAAE,MAAM,GAAEC,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGF,IAAE,CAAC;AAAE,SAAOE,OAAIA,KAAE,GAAG,GAAGF,EAAC,GAAEC,IAAED,GAAE,IAAEC,GAAE,SAAOA,GAAE,OAAO,GAAED,GAAE,EAAE,KAAKE,EAAC,GAAEF,GAAE,IAAE,OAAIC,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,IAAI;AAAJ,IAAO,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,SAAK,IAAER,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOG,IAAE,KAAK,MAAI,MAAI,EAAE,MAAI,QAAQ,MAAM,2FAA2F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,YAAOP,KAAE,GAAGD,KAAE,MAAK,CAAC,OAAKC,KAAE,GAAGD,EAAC,GAAEC,KAAE,IAAI,WAAWA,GAAE,KAAK,CAAAD,OAAG,MAAIA,GAAE,CAAC,GAAEA,GAAE,EAAE,KAAKC,EAAC,IAAGA;AAAE,QAAID,IAAEC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMD,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa,WAAW,CAAAC,KAAE,IAAI,WAAWD,EAAC;AAAA,eAAUA,cAAa,aAAa,CAAAC,KAAE,IAAI,aAAaD,EAAC;AAAA,WAAM;AAAC,YAAG,EAAEA,cAAa,cAAc,OAAM,MAAM,0BAA0BA,EAAC,EAAE;AAAE;AAAC,gBAAMD,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI;AAAE,UAAAD,GAAE,cAAcA,GAAE,QAAQ,GAAEE,KAAE,GAAGD,IAAED,IAAE,KAAK,IAAEA,GAAE,SAAOA,GAAE,OAAO,GAAEA,GAAE,YAAYA,GAAE,YAAWE,EAAC;AAAE,gBAAMC,KAAE,GAAG,IAAI;AAAE,UAAAH,GAAE,WAAWA,GAAE,YAAW,GAAEG,IAAE,KAAK,OAAM,KAAK,QAAO,GAAEH,GAAE,KAAIA,GAAE,OAAM,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGC,IAAED,IAAEE,EAAC,GAAE,GAAGD,IAAED,IAAE,QAAI,MAAI;AAAC,eAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,UAAC,EAAE,GAAE,GAAGC,EAAC,GAAE,GAAG,IAAI;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAD,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGF,IAAE,KAAK,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,IAAM,KAAG,EAAC,OAAM,SAAQ,WAAU,GAAE,QAAO,EAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,GAAG,IAAG,YAAWA,KAAEA,MAAG,CAAC,GAAG,OAAM,GAAGA,GAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,SAAOD,cAAa,WAASA,GAAEC,EAAC,IAAED;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,SAAO,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAE,KAAK,IAAI,KAAK,IAAID,IAAEC,EAAC,GAAEF,EAAC,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE,EAAE,OAAM,MAAM,oEAAoE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG,CAACA,GAAE,EAAE,OAAM,MAAM,kEAAkE;AAAE,SAAOA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,EAAE,EAAE,CAAAC,GAAED,GAAE,EAAE,CAAC;AAAA,OAAM;AAAC,UAAME,KAAEF,GAAE,GAAG,IAAEA,GAAE,GAAG,IAAEA,GAAE,GAAG;AAAE,IAAAD,GAAE,IAAEA,GAAE,KAAG,IAAI;AAAG,UAAMI,KAAE,GAAGJ,EAAC;AAAE,IAAAE,IAAGF,KAAE,IAAI,GAAG,CAACG,EAAC,GAAEF,GAAE,GAAE,OAAGG,GAAE,QAAOJ,GAAE,GAAEC,GAAE,OAAMA,GAAE,MAAM,GAAG,EAAE,CAAC,GAAED,GAAE,MAAM;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,MAAE,SAASJ,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,GAAEA,EAAC,GAAEK,KAAE,GAAGL,EAAC,GAAEQ,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAEC,IAAE,OAAI,MAAI;AAAC,MAAC,SAASL,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAEJ,GAAE;AAAE,UAAGI,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWH,EAAC,GAAEG,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC,GAAEI,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcF,EAAC,GAAEF,GAAE,MAAG,SAASA,IAAEC,IAAE;AAAC,YAAGD,OAAIC,GAAE,QAAM;AAAG,QAAAD,KAAEA,GAAE,QAAQ,GAAEC,KAAEA,GAAE,QAAQ;AAAE,mBAAS,CAACE,IAAEC,EAAC,KAAIJ,IAAE;AAAC,UAAAA,KAAEG;AAAE,gBAAME,KAAED;AAAE,cAAIF,KAAED,GAAE,KAAK;AAAE,cAAGC,GAAE,KAAK,QAAM;AAAG,gBAAK,CAACM,IAAEC,EAAC,IAAEP,GAAE;AAAM,cAAGA,KAAEO,IAAET,OAAIQ,MAAGH,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,KAAGG,GAAE,CAAC,MAAIH,GAAE,CAAC,EAAE,QAAM;AAAA,QAAE;AAAC,eAAM,CAAC,CAACD,GAAE,KAAK,EAAE;AAAA,MAAI,GAAED,GAAE,GAAEG,EAAC,EAAE,CAAAC,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC;AAAA,WAAM;AAAC,QAAAA,GAAE,IAAEG;AAAE,cAAMF,KAAE,MAAM,IAAI,EAAE,KAAK,CAAC;AAAE,QAAAE,GAAE,SAAS,CAACH,IAAEE,OAAI;AAAC,cAAG,MAAIF,GAAE,OAAO,OAAM,MAAM,kBAAkBE,EAAC,+BAA+B;AAAE,UAAAD,GAAE,IAAEC,EAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC,GAAEC,GAAE,IAAEC,KAAE,CAAC,IAAEF,GAAE,CAAC;AAAA,QAAC,EAAE,GAAEI,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWJ,GAAE,CAAC,GAAEI,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAI,GAAE,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,WAAWH,EAAC,CAAC;AAAA,MAAC;AAAA,IAAC,GAAEG,IAAEH,IAAEO,IAAEL,EAAC,GAAEE,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC;AAAE,UAAML,KAAEI,GAAE;AAAE,IAAAJ,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,EAAE;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,GAAGJ,EAAC,GAAEK,MAAE,SAASL,IAAE;AAAC,WAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAA,EAAC,GAAEA,EAAC,GAAEQ,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA,IAAEO,KAAE,MAAM,QAAQN,EAAC,IAAE,IAAI,UAAU,IAAI,kBAAkBA,EAAC,GAAE,GAAE,CAAC,IAAEA;AAAE,KAAGE,IAAED,IAAE,OAAI,MAAI;AAAC,QAAIJ,KAAEK,GAAE;AAAE,IAAAL,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAED,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWK,GAAE,CAAC,GAAEL,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcQ,EAAC,GAAER,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAWK,GAAE,CAAC,GAAEL,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcS,EAAC,GAAEL,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,IAAGJ,KAAEK,GAAE,GAAG,cAAcL,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAEA,GAAE,cAAcA,GAAE,QAAQ,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI;AAAA,EAAC,EAAE;AAAC;AAAC,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,mBAAa,OAAO,4BAA0BD,cAAa,4BAA0BA,cAAa,qCAAmC,KAAK,IAAEA,IAAE,KAAK,IAAEC,MAAG,KAAK,IAAED;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,QAAGD,IAAE;AAAC,UAAIE,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUC,MAAKL,GAAE,CAAAE,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAEH,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,IAAGL,KAAE,IAAI,UAAQ,IAAIK,GAAE,IAAEF,GAAE,OAAME,GAAE,IAAEF,GAAE,QAAO,GAAGF,GAAE,QAAO,EAAC,OAAMG,IAAE,MAAKC,GAAC,CAAC,GAAE,GAAE,IAAE,KAAK,EAAE,GAAEH,GAAE,KAAKF,EAAC,GAAEE,GAAE,OAAOF,EAAC,GAAE,EAAEI;AAAE,MAAAF,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,IAAE;AAAC,QAAGF,MAAGC,IAAE;AAAC,UAAIE,KAAE,GAAG,IAAI;AAAE,MAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK;AAAE,UAAIC,KAAED,GAAE,QAAOE,KAAE;AAAE,iBAAUG,MAAKP,IAAE;AAAC,QAAAE,GAAE,UAAU,GAAEF,KAAED,GAAEQ,GAAE,KAAK;AAAE,cAAMC,KAAET,GAAEQ,GAAE,GAAG;AAAE,QAAAP,MAAGQ,OAAIN,GAAE,cAAY,GAAGD,GAAE,OAAM,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGQ,GAAC,CAAC,GAAEN,GAAE,YAAU,GAAGD,GAAE,WAAU,EAAC,OAAMG,IAAE,MAAKJ,IAAE,IAAGQ,GAAC,CAAC,GAAEN,GAAE,OAAOF,GAAE,IAAEG,GAAE,OAAMH,GAAE,IAAEG,GAAE,MAAM,GAAED,GAAE,OAAOM,GAAE,IAAEL,GAAE,OAAMK,GAAE,IAAEL,GAAE,MAAM,IAAG,EAAEC,IAAEF,GAAE,OAAO;AAAA,MAAC;AAAC,MAAAA,GAAE,QAAQ;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,GAAGH,IAAEC,IAAE;AAAC,UAAMC,KAAE,GAAG,IAAI;AAAE,IAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK,GAAEA,GAAE,UAAU,GAAEA,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,cAAY,GAAGD,GAAE,OAAM,CAAC,CAAC,GAAEC,GAAE,YAAU,GAAGD,GAAE,WAAU,CAAC,CAAC,GAAEC,GAAE,OAAOF,GAAE,SAAQA,GAAE,OAAO,GAAEE,GAAE,OAAOF,GAAE,UAAQA,GAAE,OAAMA,GAAE,OAAO,GAAEE,GAAE,OAAOF,GAAE,UAAQA,GAAE,OAAMA,GAAE,UAAQA,GAAE,MAAM,GAAEE,GAAE,OAAOF,GAAE,SAAQA,GAAE,UAAQA,GAAE,MAAM,GAAEE,GAAE,OAAOF,GAAE,SAAQA,GAAE,OAAO,GAAEE,GAAE,OAAO,GAAEA,GAAE,KAAK,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAG,GAAE;AAAC,SAAK,KAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGJ,EAAC;AAAE,SAAGA,IAAEC,KAAG,CAAAA,OAAG;AAAC,WAAGD,IAAEC,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGD,EAAC,GAAG,UAAUI,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,EAAE;AAAA,IAAC,GAAE,MAAKD,IAAEE,IAAED,EAAC,IAAE,GAAG,MAAKD,GAAE,EAAE,GAAEE,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,SAAK,KAAE,SAASF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAMC,KAAE,GAAGJ,EAAC;AAAE,SAAGA,IAAEC,KAAG,CAAAA,OAAG;AAAC,WAAGD,IAAEC,IAAEC,IAAEC,EAAC,IAAGF,KAAE,GAAGD,EAAC,GAAG,UAAUI,GAAE,QAAO,GAAE,GAAEH,GAAE,OAAO,OAAMA,GAAE,OAAO,MAAM;AAAA,MAAC,EAAE;AAAA,IAAC,GAAE,MAAKD,IAAEC,IAAEC,EAAC,IAAE,GAAG,MAAKF,GAAE,EAAE,GAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,GAAG,MAAM,GAAE,KAAK,IAAE,QAAO,KAAK,GAAG,MAAM,GAAE,KAAK,IAAE,QAAO,KAAK,GAAG,MAAM,GAAE,KAAK,IAAE;AAAA,EAAM;AAAC;AAAE,SAAS,GAAGF,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAE,aAAOD,GAAE,EAAE,MAAM,CAAAA,OAAGA,cAAa,UAAU;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,MAAM,CAAAA,OAAG,eAAa,OAAO,eAAaA,cAAa,YAAY;AAAA,IAAE,KAAK;AAAE,aAAOA,GAAE,EAAE,MAAM,CAAAA,OAAG,eAAa,OAAO,gBAAcA,cAAa,aAAa;AAAA,IAAE;AAAQ,YAAM,MAAM,0BAA0BC,EAAC,EAAE;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,IAAAA,KAAE,GAAGD,EAAC;AAAE,UAAME,KAAE,GAAGF,EAAC,GAAEG,KAAE,IAAI,WAAWH,GAAE,QAAMA,GAAE,SAAO,CAAC;AAAE,OAAGE,IAAED,IAAE,GAAGD,EAAC,CAAC,GAAEC,GAAE,WAAW,GAAE,GAAED,GAAE,OAAMA,GAAE,QAAOC,GAAE,MAAKA,GAAE,eAAcE,EAAC,GAAE,GAAGD,EAAC,GAAED,KAAE,IAAI,UAAU,IAAI,kBAAkBE,GAAE,MAAM,GAAEH,GAAE,OAAMA,GAAE,MAAM,GAAEA,GAAE,EAAE,KAAKC,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAIC,KAAE,GAAGD,IAAE,CAAC;AAAE,MAAG,CAACC,IAAE;AAAC,UAAMC,KAAE,GAAGF,EAAC;AAAE,IAAAC,KAAE,GAAGD,EAAC;AAAE,UAAMG,KAAE,GAAGH,IAAE,CAAC,KAAG,GAAGA,EAAC;AAAE,IAAAE,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAKA,GAAE,MAAKA,GAAE,eAAcC,EAAC,GAAE,GAAGH,EAAC;AAAA,EAAC;AAAC,SAAOC;AAAC;AAAC,SAAS,GAAGD,IAAE;AAAC,MAAG,CAACA,GAAE,OAAO,OAAM,MAAM,oGAAoG;AAAE,SAAOA,GAAE,MAAIA,GAAE,IAAE,GAAGA,GAAE,OAAO,WAAW,QAAQ,GAAE,yFAAyF,IAAGA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAOA,GAAE,MAAIA,GAAE,IAAE,IAAI,OAAIA,GAAE;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,EAAAC,GAAE,SAAS,GAAE,GAAED,GAAE,OAAMA,GAAE,MAAM,GAAEC,GAAE,cAAcA,GAAE,QAAQ;AAAE,MAAIC,KAAE,GAAGF,IAAE,CAAC;AAAE,SAAOE,OAAIA,KAAE,GAAG,GAAGF,EAAC,GAAEC,EAAC,GAAED,GAAE,EAAE,KAAKE,EAAC,GAAEF,GAAE,IAAE,OAAIC,GAAE,YAAYA,GAAE,YAAWC,EAAC,GAAEA;AAAC;AAAC,SAAS,GAAGF,IAAE;AAAC,EAAAA,GAAE,EAAE,YAAYA,GAAE,EAAE,YAAW,IAAI;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,QAAMC,KAAE,GAAGD,EAAC;AAAE,SAAO,GAAG,GAAGA,EAAC,GAAEC,IAAE,OAAI,OAAI,SAASD,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE;AAAO,QAAGE,GAAE,UAAQF,GAAE,SAAOE,GAAE,WAASF,GAAE,OAAO,QAAOC,GAAE;AAAE,UAAME,KAAED,GAAE,OAAME,KAAEF,GAAE;AAAO,WAAOA,GAAE,QAAMF,GAAE,OAAME,GAAE,SAAOF,GAAE,QAAOA,KAAEC,GAAE,GAAEC,GAAE,QAAMC,IAAED,GAAE,SAAOE,IAAEJ;AAAA,EAAC,GAAEA,KAAG,MAAI;AAAC,QAAGC,GAAE,gBAAgBA,GAAE,aAAY,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,EAAED,GAAE,kBAAkB,iBAAiB,OAAM,MAAM,oGAAoG;AAAE,WAAOA,GAAE,OAAO,sBAAsB;AAAA,EAAC,EAAE,EAAE;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,qBAAmB,GAAG,UAAU,IAAG,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,OAAK,SAASA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAO,GAAGD,MAAG,KAAGH,KAAEC,OAAIC,KAAED,OAAIG,MAAG,KAAGF,KAAEF,OAAIE,KAAED,MAAIE,IAAEC,EAAC;AAAC,GAAE,GAAG,QAAM;AAAG,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAEG,IAAE;AAAC,SAAK,IAAER,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,SAAOC,IAAE,KAAK,IAAEC,IAAE,KAAK,QAAMC,IAAE,KAAK,SAAOG,KAAG,KAAK,KAAG,KAAK,OAAK,MAAI,EAAE,MAAI,QAAQ,MAAM,4FAA4F;AAAA,EAAE;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAM,CAAC,CAAC,GAAG,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,QAAIR,KAAE,GAAG,MAAK,CAAC;AAAE,WAAOA,OAAI,GAAG,IAAI,GAAE,GAAG,IAAI,GAAEA,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI,GAAE,KAAK,EAAE,KAAKA,EAAC,GAAE,KAAK,IAAE,OAAIA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,WAAO,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,UAAMA,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,GAAE;AAAC,UAAIC;AAAE,UAAGD,cAAa,UAAU,CAAAC,KAAE,IAAI,UAAUD,GAAE,MAAK,KAAK,OAAM,KAAK,MAAM;AAAA,eAAUA,cAAa,cAAa;AAAC,cAAMD,KAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI;AAAE,QAAAD,GAAE,cAAcA,GAAE,QAAQ,GAAEE,KAAE,GAAGD,IAAED,EAAC,GAAEA,GAAE,YAAYA,GAAE,YAAWE,EAAC,GAAEF,GAAE,WAAWA,GAAE,YAAW,GAAEA,GAAE,MAAK,KAAK,OAAM,KAAK,QAAO,GAAEA,GAAE,MAAKA,GAAE,eAAc,IAAI,GAAEA,GAAE,YAAYA,GAAE,YAAW,IAAI,GAAE,GAAGC,IAAED,IAAEE,EAAC,GAAE,GAAGD,IAAED,IAAE,QAAI,MAAI;AAAC,aAAG,IAAI,GAAEA,GAAE,WAAW,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,gBAAgB,GAAEA,GAAE,WAAWA,GAAE,cAAa,GAAE,CAAC,GAAE,GAAG,IAAI;AAAA,QAAC,EAAE,GAAE,GAAGC,EAAC,GAAE,GAAG,IAAI;AAAA,MAAC,OAAK;AAAC,YAAG,EAAEA,cAAa,aAAa,OAAM,MAAM,0BAA0BA,EAAC,EAAE;AAAE,WAAG,IAAI,GAAE,GAAG,IAAI,GAAEC,KAAE,GAAG,IAAI,GAAE,GAAG,IAAI;AAAA,MAAC;AAAC,MAAAF,GAAE,KAAKE,EAAC;AAAA,IAAC;AAAC,WAAO,IAAI,GAAGF,IAAE,KAAK,GAAG,GAAE,KAAK,EAAE,GAAE,KAAK,QAAO,KAAK,GAAE,KAAK,OAAM,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,KAAG,GAAG,MAAK,CAAC,EAAE,MAAM,GAAE,KAAK,KAAG,GAAG,IAAI,EAAE,cAAc,GAAG,MAAK,CAAC,CAAC,GAAE,KAAG;AAAA,EAAE;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,QAAM,GAAG,UAAU,OAAM,GAAG,UAAU,oBAAkB,GAAG,UAAU,GAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,GAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,IAAG,GAAG,UAAU,eAAa,GAAG,UAAU;AAAG,IAAI,KAAG;AAAI,SAAS,MAAMA,IAAE;AAAC,SAAOA,GAAE,KAAK,CAAC,CAACA,IAAEC,EAAC,OAAK,EAAC,OAAMD,IAAE,KAAIC,GAAC,GAAG;AAAC;AAAC,IAAM,KAAG,0BAASD,IAAE;AAAC,SAAO,cAAcA,GAAC;AAAA,IAAC,KAAI;AAAC,WAAK,EAAE,oCAAoC;AAAA,IAAC;AAAA,EAAC;AAAC,IAAG,KAAG,MAAK;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,IAAE,MAAG,KAAK,IAAED,IAAE,KAAK,IAAE,MAAK,KAAK,IAAE,GAAE,KAAK,IAAE,cAAY,OAAO,KAAK,EAAE,sBAAqB,WAASC,KAAE,KAAK,EAAE,SAAOA,KAAE,GAAG,IAAE,KAAK,EAAE,SAAO,IAAI,gBAAgB,GAAE,CAAC,KAAG,QAAQ,KAAK,oHAAoH,GAAE,KAAK,EAAE,SAAO,SAAS,cAAc,QAAQ;AAAA,EAAE;AAAA,EAAC,MAAM,gBAAgBD,IAAE;AAAC,UAAMC,KAAE,OAAM,MAAM,MAAMD,EAAC,GAAG,YAAY;AAAE,IAAAA,KAAE,EAAEA,GAAE,SAAS,QAAQ,KAAGA,GAAE,SAAS,YAAY,IAAG,KAAK,SAAS,IAAI,WAAWC,EAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,SAAU,IAAI,cAAa,OAAOA,EAAC,GAAE,KAAE;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE,QAAOG,KAAE,KAAK,EAAE,QAAQD,EAAC;AAAE,SAAK,EAAE,OAAO,IAAIF,IAAEG,EAAC,GAAEF,KAAE,KAAK,EAAE,mBAAmBC,IAAEC,EAAC,IAAE,KAAK,EAAE,iBAAiBD,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAK,EAAE,mBAAiB,QAAQ,KAAK,kHAAkH,GAAE,GAAG,MAAKD,MAAG,gBAAe,CAAAA,OAAG;AAAC,SAAG,MAAKC,KAAEA,MAAG,iBAAgB,CAAAA,OAAG;AAAC,aAAK,EAAE,gBAAgBD,IAAEC,IAAEJ,IAAEC,MAAG,GAAEC,EAAC;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAE;AAAC,SAAK,IAAEA;AAAA,EAAC;AAAA,EAAC,sBAAsBA,IAAE;AAAC,SAAK,EAAE,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAE;AAAC,SAAK,EAAE,sCAAoCA;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAE;AAAC,OAAG,MAAK,qBAAoB,CAAAC,OAAG;AAAC,MAAAD,GAAEC,EAAC;AAAA,IAAC,EAAE,GAAE,GAAG,MAAK,qBAAoB,CAAAD,OAAG;AAAC,WAAK,EAAE,gBAAgBA,IAAE,MAAM;AAAA,IAAC,EAAE,GAAE,OAAO,KAAK,EAAE,gBAAgB;AAAA,EAAgB;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,EAAE,gBAAcA;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEC,IAAE;AAAC,SAAK,EAAE,uBAAqB,KAAK,EAAE,wBAAsB,CAAC,GAAE,KAAK,EAAE,qBAAqBD,EAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEC,IAAEC,IAAE;AAAC,SAAK,0BAA0BF,IAAE,GAAE,GAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAEL,GAAE;AAAO,SAAK,MAAIK,OAAI,KAAK,KAAG,KAAK,EAAE,MAAM,KAAK,CAAC,GAAE,KAAK,IAAE,KAAK,EAAE,QAAQA,EAAC,GAAE,KAAK,IAAEA,KAAG,KAAK,EAAE,QAAQ,IAAIL,IAAE,KAAK,IAAE,CAAC,GAAE,GAAG,MAAKG,KAAG,CAAAH,OAAG;AAAC,WAAK,EAAE,uBAAuB,KAAK,GAAEC,IAAEC,IAAEF,IAAEI,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBJ,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKJ,IAAEC,EAAC;AAAE,WAAK,EAAE,yBAAyBA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,wBAAwBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,uBAAuBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBD,IAAEC,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,wBAAwBA,IAAEC,IAAEC,EAAC;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,SAAG,MAAK,OAAO,KAAKD,EAAC,IAAG,CAAAG,OAAG;AAAC,WAAG,MAAK,OAAO,OAAOH,EAAC,IAAG,CAAAI,OAAG;AAAC,eAAK,EAAE,6BAA6BD,IAAEC,IAAE,OAAO,KAAKJ,EAAC,EAAE,QAAOC,IAAEC,EAAC;AAAA,QAAC,EAAE;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,cAAMG,KAAE,KAAK,EAAE,QAAQJ,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEI,EAAC,GAAE,KAAK,EAAE,uBAAuBA,IAAEJ,GAAE,QAAOC,IAAEC,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBJ,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,6BAA6BA,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,6CAA6C;AAAE,iBAAUF,MAAKD,GAAE,MAAK,EAAE,oBAAoBG,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKD,GAAE,MAAK,EAAE,sBAAsBG,IAAEF,EAAC;AAAE,WAAK,EAAE,8BAA8BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,qBAAqBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,8CAA8C;AAAE,iBAAUF,MAAKD,GAAE,MAAK,EAAE,qBAAqBG,IAAEF,EAAC;AAAE,WAAK,EAAE,6BAA6BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,mBAAmBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,4CAA4C;AAAE,iBAAUF,MAAKD,GAAE,MAAK,EAAE,mBAAmBG,IAAEF,EAAC;AAAE,WAAK,EAAE,2BAA2BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,sBAAsBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,oBAAoBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,qDAAqD;AAAE,iBAAUF,MAAKD,GAAE,MAAK,EAAE,oBAAoBG,IAAEF,EAAC;AAAE,WAAK,EAAE,4BAA4BE,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAME,KAAE,KAAK,EAAE,sBAAsBH,GAAE,MAAM;AAAE,UAAG,CAACG,GAAE,OAAM,MAAM,+CAA+C;AAAE,iBAAUF,MAAKD,GAAE,IAAG,MAAKC,KAAG,CAAAD,OAAG;AAAC,aAAK,EAAE,sBAAsBG,IAAEH,EAAC;AAAA,MAAC,EAAE;AAAE,WAAK,EAAE,8BAA8BG,IAAEF,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBF,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BD,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BD,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BD,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBD,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BD,IAAEC,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,aAAK,EAAE,4BAA4BA,IAAEC,EAAC;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BD,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,SAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,cAAME,KAAE,KAAK,EAAE,QAAQH,GAAE,MAAM;AAAE,aAAK,EAAE,OAAO,IAAIA,IAAEG,EAAC,GAAE,KAAK,EAAE,2BAA2BA,IAAEH,GAAE,QAAOC,IAAEC,EAAC,GAAE,KAAK,EAAE,MAAMC,EAAC;AAAA,MAAC,EAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BH,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,6CAA6C;AAAE,iBAAUD,MAAKD,GAAE,MAAK,EAAE,oBAAoBE,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKD,GAAE,MAAK,EAAE,sBAAsBE,IAAED,EAAC;AAAE,WAAK,EAAE,kCAAkCC,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,gCAAgCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,qBAAqBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,8CAA8C;AAAE,iBAAUD,MAAKD,GAAE,MAAK,EAAE,qBAAqBE,IAAED,EAAC;AAAE,WAAK,EAAE,iCAAiCC,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,8BAA8BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,mBAAmBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,4CAA4C;AAAE,iBAAUD,MAAKD,GAAE,MAAK,EAAE,mBAAmBE,IAAED,EAAC;AAAE,WAAK,EAAE,+BAA+BC,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,oBAAoBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,qDAAqD;AAAE,iBAAUD,MAAKD,GAAE,MAAK,EAAE,oBAAoBE,IAAED,EAAC;AAAE,WAAK,EAAE,gCAAgCC,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,iCAAiCD,IAAEC,IAAE;AAAC,OAAG,MAAKA,KAAG,CAAAA,OAAG;AAAC,YAAMC,KAAE,KAAK,EAAE,sBAAsBF,GAAE,MAAM;AAAE,UAAG,CAACE,GAAE,OAAM,MAAM,+CAA+C;AAAE,iBAAUD,MAAKD,GAAE,IAAG,MAAKC,KAAG,CAAAD,OAAG;AAAC,aAAK,EAAE,sBAAsBE,IAAEF,EAAC;AAAA,MAAC,EAAE;AAAE,WAAK,EAAE,kCAAkCE,IAAED,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,mBAAmBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,yBAAyBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,oBAAoBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,0BAA0BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,sBAAsBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,4BAA4BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKF,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEE,MAAG,KAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKF,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,2BAA2BA,IAAEE,MAAG,KAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBF,IAAEC,IAAEC,IAAE;AAAC,SAAK,EAAE,wBAAsB,QAAQ,KAAK,4HAA4H,GAAE,GAAG,MAAKF,KAAG,CAACA,IAAEE,OAAI;AAAC,MAAAF,KAAE,IAAI,aAAaA,GAAE,QAAOA,GAAE,YAAWA,GAAE,SAAO,CAAC,GAAEC,GAAED,IAAEE,EAAC;AAAA,IAAC,EAAE,GAAE,GAAG,MAAKF,KAAG,CAAAA,OAAG;AAAC,WAAK,EAAE,qBAAqBA,IAAEE,MAAG,KAAE;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,SAAK,EAAE,eAAe;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,EAAE,YAAY,GAAE,KAAK,EAAE,kBAAgB,QAAO,KAAK,EAAE,uBAAqB;AAAA,EAAM;AAAC,GAAE,cAAc,GAAE;AAAA,EAAC,IAAI,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGF,IAAEC,IAAEC,IAAE;AAAC,OAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,YAAK,CAACE,IAAEC,EAAC,IAAE,GAAG,MAAKJ,IAAEC,EAAC;AAAE,WAAK,GAAG,gCAAgCA,IAAEE,IAAEC,IAAEF,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,EAAEF,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,qBAAqBA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,OAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,MAAKD,KAAG,CAAAA,OAAG;AAAC,WAAK,GAAG,2BAA2BA,EAAC;AAAA,IAAC,EAAE;AAAA,EAAC;AAAC,EAAE;AAAE,IAAI;AAAJ,IAAO,KAAG,cAAc,GAAE;AAAC;AAAE,eAAe,GAAGA,IAAEC,IAAEC,IAAE;AAAC,UAAO,eAAeF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAGH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC,GAAEH,IAAEE,GAAE,WAAS,GAAG,IAAE,SAAO,SAAS,cAAc,QAAQ,IAAGD,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGF,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGH,GAAE,GAAE;AAAC,UAAMK,KAAE,IAAI;AAAG,QAAGH,IAAG,kBAAiB;AAAC,UAAG,CAACF,GAAE,GAAG,OAAM,MAAM,+CAA+C;AAAE,UAAII,KAAEF,GAAE;AAAiB,UAAGE,GAAE,QAAMA,GAAE,SAAOA,GAAE,OAAKA,GAAE,OAAO,OAAM,MAAM,oDAAoD;AAAE,UAAGA,GAAE,OAAK,KAAGA,GAAE,MAAI,KAAGA,GAAE,QAAM,KAAGA,GAAE,SAAO,EAAE,OAAM,MAAM,uCAAuC;AAAE,SAAGC,IAAE,IAAGD,GAAE,OAAKA,GAAE,SAAO,CAAC,GAAE,GAAGC,IAAE,IAAGD,GAAE,MAAIA,GAAE,UAAQ,CAAC,GAAE,GAAGC,IAAE,GAAED,GAAE,QAAMA,GAAE,IAAI,GAAE,GAAGC,IAAE,GAAED,GAAE,SAAOA,GAAE,GAAG;AAAA,IAAC,MAAM,IAAGC,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAE,CAAC,GAAE,GAAGA,IAAE,GAAE,CAAC;AAAE,QAAGH,IAAG,iBAAgB;AAAC,UAAGA,IAAG,kBAAgB,MAAI,EAAE,OAAM,MAAM,4CAA4C;AAAE,UAAG,GAAGG,IAAE,GAAE,CAAC,KAAK,KAAGH,GAAE,kBAAgB,GAAG,GAAEA,IAAG,kBAAgB,OAAK,GAAE;AAAC,cAAK,CAACF,IAAEG,EAAC,IAAE,GAAGF,EAAC;AAAE,QAAAC,KAAE,GAAGG,IAAE,CAAC,IAAEF,KAAEH,IAAEI,KAAE,GAAGC,IAAE,CAAC,IAAEL,KAAEG,IAAE,GAAGE,IAAE,GAAEH,EAAC,GAAE,GAAGG,IAAE,GAAED,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAJ,GAAE,EAAE,iBAAiBK,GAAE,EAAE,GAAE,4BAA2BL,GAAE,GAAEG,EAAC;AAAA,EAAC;AAAC,EAAAH,GAAE,EAAE,GAAGC,IAAED,GAAE,GAAEG,MAAG,YAAY,IAAI,CAAC,GAAEH,GAAE,iBAAiB;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAE;AAAC,MAAGF,GAAE,aAAa,EAAE,EAAE,OAAM,MAAM,gFAAgF;AAAE,KAAGA,IAAEC,IAAEC,IAAEF,GAAE,IAAE,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACH,GAAE,aAAa,EAAE,EAAE,OAAM,MAAM,gFAAgF;AAAE,KAAGA,IAAEC,IAAEC,IAAEC,EAAC;AAAC;AAAC,SAAS,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAEH,GAAE;AAAK,QAAMI,KAAEJ,GAAE,OAAMO,KAAEH,MAAGJ,KAAEA,GAAE;AAAQ,OAAIG,cAAa,cAAYA,cAAa,iBAAeA,GAAE,WAASI,GAAE,OAAM,MAAM,gCAA8BJ,GAAE,SAAOI,EAAC;AAAE,SAAOR,KAAE,IAAI,GAAG,CAACI,EAAC,GAAEF,IAAE,OAAGF,GAAE,EAAE,EAAE,QAAOA,GAAE,GAAEK,IAAEJ,EAAC,GAAEE,KAAEH,GAAE,MAAM,IAAEA;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMH,EAAC,GAAE,KAAK,IAAEA,IAAE,KAAK,IAAEC,IAAE,KAAK,IAAEC,IAAE,KAAK,KAAGC,IAAE,KAAK,IAAE,IAAI;AAAA,EAAE;AAAA,EAAC,EAAEH,IAAEC,KAAE,MAAG;AAAC,QAAG,iBAAgBD,MAAG,GAAG,KAAK,aAAY,GAAE,CAAC,CAACA,GAAE,eAAa,YAAUA,GAAE,WAAW,GAAE,WAASA,GAAE,UAAQ,KAAK,EAAE,EAAE,WAASA,GAAE,OAAO,OAAM,MAAM,iDAAiD;AAAE,WAAO,MAAM,EAAEA,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,EAAE,MAAM,GAAE,MAAM,MAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,4BAA2BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,0BAAwB,GAAE,GAAE,6BAA4BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,2BAAyB,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,cAAc,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,wBAAwB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAArX,IAAuX,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA5hB,IAA8hB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAnnB,IAAqnB,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAlqB,IAAoqB,KAAG,GAAG,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAn0B,IAAq0B,KAAG,GAAG,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,CAAC;AAA74B,IAA+4B,KAAG,GAAG,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAA57B,IAA87B,KAAG,GAAG,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,CAAC;AAA/xC,IAAiyC,KAAG,CAAC,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,IAAG,GAAG,EAAE;AAAx0C,IAA00C,KAAG,GAAG,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,IAAG,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,GAAG,GAAE,CAAC,KAAI,EAAE,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,GAAE,CAAC,KAAI,GAAG,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,8BAA6B,CAAC,EAAC,GAAE,KAAK,qCAAmC,KAAK,wBAAsB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,wCAAuCA,OAAI,KAAK,qCAAmC,CAAC,CAACA,GAAE,qCAAoC,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,cAAc,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,aAAa,GAAE,GAAGE,IAAE,yBAAyB,GAAE,KAAK,EAAE,0BAA0B,gBAAe,CAACF,IAAEC,OAAI;AAAC,UAAG,KAAK,sBAAsB,YAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,gBAAgB,KAAK,GAAGD,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,gBAAe,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,KAAK,uCAAqC,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAACF,IAAEC,OAAI;AAAC,UAAG,KAAK,mCAAmC,YAAUA,MAAKD,GAAE,EAACA,KAAE,GAAG,GAAGC,EAAC,GAAE,IAAG,CAAC,MAAI,KAAK,EAAE,6BAA6B,KAAK,EAAC,MAAK,GAAGD,IAAE,CAAC,KAAG,KAAG,GAAE,SAAQ,GAAGA,IAAE,CAAC,KAAG,KAAG,GAAE,MAAK,GAAGA,IAAE,GAAE,IAAG,GAAG,CAAC,EAAE,MAAM,KAAG,CAAC,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,QAAG,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,MAAKF,IAAEG,MAAG,CAAC,CAAC,GAAE,CAAC,KAAK,EAAE,QAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIH,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wDAAwD,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,+BAA+B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,EAAE,mBAAkB,CAACF,IAAEC,OAAI;AAAC,UAAIC,KAAE,CAAC,KAAK,GAAEC,KAAEH,GAAE,MAAKI,KAAEJ,GAAE;AAAM,YAAMK,KAAED,MAAGJ,KAAEA,GAAE;AAAQ,UAAGG,cAAa,WAAW,KAAGA,GAAE,WAAS,IAAEE,IAAE;AAAC,cAAMJ,KAAE,IAAI,kBAAkB,IAAEI,EAAC;AAAE,iBAAQL,KAAE,GAAEA,KAAEK,IAAE,EAAEL,GAAE,CAAAC,GAAE,IAAED,EAAC,IAAEG,GAAE,IAAEH,EAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAEG,GAAE,IAAEH,KAAE,CAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAEG,GAAE,IAAEH,KAAE,CAAC,GAAEC,GAAE,IAAED,KAAE,CAAC,IAAE;AAAI,QAAAG,KAAE,IAAI,UAAUF,IAAEG,IAAEJ,EAAC;AAAA,MAAC,OAAK;AAAC,YAAGG,GAAE,WAAS,IAAEE,GAAE,OAAM,MAAM,gCAA8BF,GAAE,SAAOE,EAAC;AAAE,QAAAF,KAAE,IAAI,UAAU,IAAI,kBAAkBA,GAAE,QAAOA,GAAE,YAAWA,GAAE,MAAM,GAAEC,IAAEJ,EAAC;AAAA,MAAC;AAAA,eAAS,EAAEG,cAAa,cAAc,OAAM,MAAM,uBAAuBA,GAAE,YAAY,IAAI,EAAE;AAAE,MAAAC,KAAE,IAAI,GAAG,CAACD,EAAC,GAAE,OAAG,OAAG,KAAK,EAAE,EAAE,QAAO,KAAK,GAAEC,IAAEJ,EAAC,GAAE,KAAK,IAAEE,KAAEA,KAAEE,GAAE,MAAM,IAAEA,IAAE,KAAK,KAAG,KAAK,EAAEF,EAAC,GAAE,GAAG,MAAKD,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,WAAK,IAAE,MAAK,KAAK,KAAG,KAAK,EAAE,IAAI,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,WAAS,CAAC,GAAEA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,aAAW,CAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,SAAO,MAAIA,GAAE,SAAS,SAAO,EAAC,UAAS,CAAC,GAAE,WAAU,CAAC,GAAE,gBAAe,CAAC,GAAE,YAAW,CAAC,GAAE,cAAa,CAAC,EAAC,IAAE,EAAC,UAASA,GAAE,UAAS,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,YAAW,cAAaA,GAAE,WAAU;AAAC;AAAC,SAAS,GAAGA,IAAEC,KAAE,MAAG;AAAC,QAAMC,KAAE,CAAC;AAAE,aAAUE,MAAKJ,IAAE;AAAC,QAAIG,KAAE,GAAGC,EAAC;AAAE,IAAAJ,KAAE,CAAC;AAAE,eAAUE,MAAKC,GAAE,EAAE,EAAE,CAAAA,KAAEF,MAAG,QAAM,GAAGC,IAAE,CAAC,IAAE,GAAGA,IAAE,CAAC,KAAG,IAAE,IAAGF,GAAE,KAAK,EAAC,OAAM,GAAGE,IAAE,CAAC,KAAG,GAAE,OAAMC,IAAE,cAAa,GAAGD,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,CAAC;AAAE,IAAAA,GAAE,KAAKF,EAAC;AAAA,EAAC;AAAC,SAAOE;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAEA,GAAE,iCAAgC;AAAC,UAAIC,KAAE,IAAI,MAAGC,KAAED,IAAEE,KAAE,GAAGH,GAAE,iCAAgC,GAAG,KAAK,GAAE,IAAG,CAAC,GAAG,EAAE,CAAC;AAAE,SAAGE,IAAE,GAAE,GAAEC,EAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEF,EAAC;AAAA,IAAC,MAAM,YAASD,GAAE,mCAAiC,GAAG,KAAK,GAAE,IAAG,CAAC,GAAG,EAAE;AAAE,WAAOA,GAAE,mCAAiC,GAAGE,KAAED,KAAE,IAAI,MAAG,GAAE,GAAEE,KAAE,GAAGH,GAAE,iCAAgC,GAAG,KAAK,GAAE,IAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAEC,EAAC,KAAG,WAASD,GAAE,mCAAiC,GAAG,KAAK,GAAE,IAAG,CAAC,GAAG,EAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,eAAe,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,kEAAkE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,6BAA6B,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGD,IAAE,IAAG,CAAC,EAAE,CAAAE,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,UAAU,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAACA,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAK,GAAGD,IAAE,IAAG,CAAC,EAAE,CAAAE,GAAE,KAAK,EAAC,GAAE,GAAGD,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,YAAW,GAAGA,IAAE,CAAC,KAAG,EAAC,CAAC;AAAE,aAAK,eAAe,KAAKC,EAAC;AAAA,MAAC;AAAC,SAAG,MAAKD,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAACA,IAAEC,OAAI;AAAC,WAAK,SAAS,KAAK,GAAG,GAAGD,IAAE,KAAE,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAACA,IAAEC,OAAI;AAAC,WAAK,WAAW,KAAK,GAAG,GAAGD,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,SAAM,EAAC,WAAUA,GAAE,WAAU,gBAAeA,GAAE,gBAAe,cAAaA,GAAE,YAAW,YAAWA,GAAE,WAAU;AAAC;AAAC,GAAG,UAAU,oBAAkB,GAAG,UAAU,IAAG,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,0BAA0B,GAAE,GAAGA,IAAE,sCAAsC,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAACA,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,YAAWC,KAAED,GAAE;AAAK,YAAME,KAAE,CAAC;AAAE,iBAAUH,MAAKD,IAAE;AAAC,QAAAA,KAAE,GAAGC,EAAC;AAAE,cAAMC,KAAE,CAAC;AAAE,mBAAUD,MAAKD,GAAE,EAAE,EAAE,CAAAE,GAAE,KAAK,EAAC,OAAM,GAAGD,IAAE,CAAC,KAAG,GAAE,OAAM,GAAGA,IAAE,CAAC,KAAG,KAAG,IAAG,cAAa,GAAGA,IAAE,CAAC,KAAG,MAAI,IAAG,aAAY,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE,CAAC;AAAE,QAAAG,GAAE,KAAKF,EAAC;AAAA,MAAC;AAAC,MAAAC,GAAE,KAAKD,IAAE,GAAGE,EAAC,GAAE,GAAG,MAAKH,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;AAAG,IAAI,KAAG,GAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC;AAAE,SAAS,GAAGD,IAAE;AAAC,EAAAA,GAAE,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,QAAG,CAACA,GAAE,EAAE,QAAOA,GAAE;AAAE,IAAAA,GAAE,EAAEA,GAAE,CAAC;AAAA,EAAC,UAAC;AAAQ,OAAGA,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,GAAGA,IAAEC,IAAE;AAAC,EAAAD,KAAE,GAAGA,EAAC,GAAEC,GAAE,KAAK,GAAGD,EAAC,CAAC;AAAC;AAAC,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,sBAAqB,MAAK,KAAE,GAAE,KAAK,IAAE,EAAC,eAAc,CAAC,GAAE,iBAAgB,CAAC,GAAE,eAAc,CAAC,GAAE,oBAAmB,CAAC,GAAE,uBAAsB,CAAC,GAAE,mBAAkB,CAAC,GAAE,wBAAuB,CAAC,GAAE,oBAAmB,CAAC,GAAE,yBAAwB,CAAC,EAAC,GAAE,KAAK,8BAA4B,KAAK,wBAAsB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,2BAA0BA,OAAI,KAAK,wBAAsB,CAAC,CAACA,GAAE,wBAAuB,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,iCAAgCA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,+BAA6B,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,iCAAgCA,OAAI,KAAK,8BAA4B,CAAC,CAACA,GAAE,8BAA6B,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,oBAAoB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,4BAA4B;AAAE,UAAMC,KAAE,IAAI,MAAGC,KAAE,IAAI;AAAG,OAAGA,IAAE,GAAE,GAAG,qGAAqG,GAAE,EAAE,IAAE,SAASF,IAAEC,IAAE;AAAC,UAAG,QAAMA,GAAE,KAAG,MAAM,QAAQA,EAAC,EAAE,IAAGD,IAAE,GAAE,GAAGC,IAAE,IAAG,QAAO,QAAO,KAAE,CAAC;AAAA,WAAM;AAAC,YAAG,EAAE,YAAU,OAAOA,MAAGA,cAAa,KAAG,EAAEA,EAAC,GAAG,OAAM,MAAM,uCAAqCA,KAAE,+EAA+E;AAAE,WAAGD,IAAE,GAAE,GAAGC,IAAE,KAAE,GAAE,EAAE,CAAC;AAAA,MAAC;AAAA,IAAC,GAAEC,IAAE,KAAK,EAAE,EAAE,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,oEAAoE,GAAE,GAAGA,IAAE,GAAE,IAAGD,EAAC,GAAE,GAAGC,IAAE,0BAA0B,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,+BAA+B,GAAE,GAAGA,IAAE,yCAAyC,GAAE,GAAGA,IAAE,qDAAqD,GAAE,GAAGA,IAAE,2CAA2C,GAAE,GAAGA,IAAE,uDAAuD,GAAEA,GAAE,EAAEF,EAAC,GAAE,GAAGD,IAAEG,EAAC,GAAE,GAAG,MAAKH,EAAC,GAAE,KAAK,EAAE,oBAAoB,mBAAkB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,oBAAoB,yBAAwB,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAmB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,gCAA8B,GAAGG,IAAE,+CAA+C,GAAE,GAAG,MAAK,wBAAwB,GAAE,KAAK,EAAE,EAAE,2BAA0B,CAACH,IAAEC,OAAI;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAG,MAAKD,IAAE,MAAG,CAAC,KAAK,CAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,2BAA0B,CAAAD,OAAG;AAAC,WAAK,EAAE,wBAAsB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,KAAK,EAAE,oBAAoB,mBAAkB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,aAAa,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,0BAAwB,GAAGA,IAAE,mBAAmB,GAAE,GAAGG,IAAE,oCAAoC,GAAE,KAAK,EAAE,oBAAoB,sBAAqB,CAACH,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAgB,WAAK,0BAAwBF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,GAAE,EAAE,KAAG,CAAC,CAAC,CAAC,IAAG,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,sBAAqB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,KAAK,EAAE,oBAAoB,wBAAuB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,iBAAiB,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,wBAAuB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,oBAAoB,8BAA6B,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAuB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,8BAA6B,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,oBAAoB,yBAAwB,CAACA,IAAEC,OAAI;AAAC,SAAGD,IAAE,KAAK,EAAE,kBAAkB,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,oBAAoB,+BAA8B,CAACA,IAAEC,OAAI;AAAC,UAAIC,KAAE,KAAK,EAAE;AAAwB,MAAAF,KAAE,GAAGA,EAAC,GAAEE,GAAE,KAAK,GAAGF,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,+BAA8B,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB,IAAG,GAAG,mBAAiB,IAAG,GAAG,sBAAoB,IAAG,GAAG,0BAAwB,IAAG,GAAG,8BAA4B,IAAG,GAAG,2BAAyB,IAAG,GAAG,2BAAyB,IAAG,GAAG,+BAA6B,IAAG,GAAG,4BAA0B,IAAG,GAAG,2BAAyB,IAAG,GAAG,0BAAwB,IAAG,GAAG,6BAA2B;AAAG,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,eAAc,aAAY,IAAE,GAAE,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,GAAG,KAAK,GAAE,GAAE,GAAE,GAAGA,IAAE,GAAG,KAAK,GAAE,IAAG,CAAC,CAAC,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,iBAAgB,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,aAAa,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,iBAAiB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,8DAA8D,GAAE,GAAGA,IAAE,mBAAmB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,oBAAoB,oBAAmB,CAACF,IAAEC,OAAI;AAAC,WAAK,KAAE,SAASD,IAAE;AAAC,cAAMC,KAAE,EAAC,iBAAgB,GAAGD,IAAE,IAAG,CAAC,EAAE,KAAK,CAAAA,OAAG,GAAG,GAAGA,IAAE,IAAG,CAAC,GAAG,EAAE,KAAG,CAAC,GAAE,GAAGA,IAAE,CAAC,KAAG,GAAE,GAAGA,IAAE,CAAC,KAAG,EAAE,EAAE,EAAC;AAAE,eAAO,QAAM,GAAG,GAAGA,IAAE,CAAC,CAAC,MAAIC,GAAE,cAAY,GAAG,GAAGD,IAAE,CAAC,CAAC,KAAG,IAAGC;AAAA,MAAC,GAAE,GAAGD,EAAC,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAmB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,mBAAiB,GAAG,UAAU,IAAG,GAAG,UAAU,WAAS,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,KAAK,aAAW,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAIC,KAAE,KAAK,GAAEC,KAAE,GAAG,KAAK,GAAE,IAAG,CAAC;AAAE,WAAOA,KAAEA,KAAEA,GAAE,MAAM,IAAE,IAAI,MAAG,WAASF,GAAE,cAAY,GAAGE,IAAE,GAAEF,GAAE,WAAW,IAAE,iBAAgBA,MAAG,GAAGE,IAAE,CAAC,GAAE,WAASF,GAAE,WAAS,GAAGE,IAAE,GAAEF,GAAE,QAAQ,IAAE,cAAaA,MAAG,GAAGE,IAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,GAAEC,EAAC,GAAE,KAAK,EAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,GAAGD,IAAEC,IAAEC,IAAE;AAAC,WAAO,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,gBAAgB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,0DAA0D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,2BAA2B,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,oBAAoB,mBAAkB,CAACF,IAAEC,OAAI;AAAC,MAAAD,KAAE,GAAGA,EAAC,GAAE,KAAK,cAAW,SAASA,IAAE;AAAC,eAAM,EAAC,YAAW,GAAGA,IAAE,IAAG,CAAC,EAAE,KAAK,CAAAA,OAAG;AAAC,gBAAMC,KAAE,EAAC,WAAU,GAAGD,IAAE,CAAC,KAAG,KAAG,IAAG,UAAS,GAAGA,IAAE,CAAC,KAAG,MAAI,GAAE;AAAE,cAAG,WAAS,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,EAAE,CAAAA,KAAE,GAAGA,KAAE,GAAGA,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,GAAE,GAAE,IAAG,GAAG,CAAC,GAAEC,GAAE,iBAAeD,GAAE,MAAM;AAAA,eAAM;AAAC,kBAAME,KAAE,IAAI,WAAW,CAAC;AAAE,YAAAD,GAAE,qBAAmB,GAAGD,IAAE,IAAG,GAAGA,IAAE,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,KAAGE;AAAA,UAAC;AAAC,iBAAOD;AAAA,QAAC,EAAE,GAAE,aAAY,GAAG,GAAGD,IAAE,CAAC,CAAC,KAAG,EAAC;AAAA,MAAC,GAAEA,EAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,mBAAiB,SAASA,IAAEC,IAAE;AAAC,MAAGD,GAAE,kBAAgBC,GAAE,eAAe,CAAAD,KAAE,GAAGA,GAAE,gBAAeC,GAAE,cAAc;AAAA,OAAM;AAAC,QAAG,CAACD,GAAE,sBAAoB,CAACC,GAAE,mBAAmB,OAAM,MAAM,0EAA0E;AAAE,IAAAD,KAAE,GAAG,GAAGA,GAAE,kBAAkB,GAAE,GAAGC,GAAE,kBAAkB,CAAC;AAAA,EAAC;AAAC,SAAOD;AAAC,GAAE,GAAG,UAAU,gBAAc,GAAG,UAAU,IAAG,GAAG,UAAU,QAAM,GAAG,UAAU,IAAG,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,kBAAgBF,IAAE,KAAK,eAAaC,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,iBAAiB,SAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,EAAE,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,eAAa,QAAOA,GAAE,kBAAgB,QAAOA,GAAE,gBAAc;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAE,IAAI,GAAGD,GAAE,iBAAgBA,GAAE,cAAaA,GAAE,aAAa;AAAE,QAAG,CAACA,GAAE,EAAE,QAAOC;AAAE,IAAAD,GAAE,EAAEC,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGD,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,IAAE,CAAC,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,MAAC,SAASA,IAAE;AAAC,YAAMC,KAAE,GAAGD,GAAE,GAAG,GAAE,IAAG,CAAC,EAAE,QAAQ,CAAAA,QAAI,GAAGA,IAAE,CAAC,KAAG,IAAI,SAAS,iDAAiD,EAAE;AAAE,UAAGA,GAAE,IAAE,CAAC,GAAEC,GAAE,SAAO,EAAE,OAAM,MAAM,8EAA8E;AAAE,YAAIA,GAAE,WAAS,GAAGA,GAAE,CAAC,GAAE,IAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAG,oBAAI,OAAK,SAAS,CAACA,IAAEC,OAAI;AAAC,QAAAF,GAAE,EAAE,OAAOE,EAAC,CAAC,IAAE,GAAGD,IAAE,CAAC,KAAG;AAAA,MAAE,EAAE;AAAA,IAAC,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,QAAQD,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,GAAGH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,KAAI;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGE,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,qBAAoB,CAACF,IAAEC,OAAI;AAAC,WAAK,kBAAgBD,GAAE,KAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,EAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,qBAAoB,CAAAD,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,kBAAiB,CAACF,IAAEC,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKD,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGE,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAACF,IAAEC,OAAI;AAAC,WAAK,gBAAcD,IAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,YAAU,GAAG,UAAU,IAAG,GAAG,UAAU,kBAAgB,GAAG,UAAU,IAAG,GAAG,UAAU,UAAQ,GAAG,UAAU,SAAQ,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,kBAAgBF,IAAE,KAAK,eAAaC,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,iBAAiB,SAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,EAAE,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAhD,IAAkD,KAAG,CAAC,GAAE,IAAG,EAAE;AAA7D,IAA+D,KAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE;AAAnF,IAAqF,KAAG,CAAC,GAAE,EAAE;AAA7F,IAA+F,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA7G,IAA+G,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAA3J,IAA6J,KAAG,CAAC,GAAE,IAAG,IAAG,EAAE;AAA3K,IAA6K,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAzN,IAA2N,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAvQ,IAAyQ,KAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAG,EAAE;AAAvS,IAAyS,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,IAAE,GAAG,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,EAAE,GAAE,IAAG,IAAG,IAAG,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,IAAG,CAAC,GAAE,IAAG,EAAE,CAAC,GAAE,IAAG,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,CAAC,CAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,gBAAe,KAAE,GAAE,KAAK,qBAAmB,OAAG,KAAK,wBAAsB,MAAG,KAAK,IAAE,IAAI,MAAG,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAGD,KAAE,KAAK,GAAE,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,wBAAuBA,OAAI,KAAK,qBAAmBA,GAAE,sBAAoB,QAAI,2BAA0BA,OAAI,KAAK,wBAAsBA,GAAE,yBAAuB,OAAI,MAAM,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,SAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,KAAK,gBAAc,KAAK,eAAa,KAAK,kBAAgB,QAAOD,KAAE,KAAK,IAAE,GAAEC,KAAE,IAAI;AAAG,UAAME,KAAE,IAAI;AAAG,QAAIG,KAAE,IAAI;AAAG,QAAG,GAAGA,IAAE,GAAE,GAAG,GAAE,GAAGH,IAAE,GAAE,IAAGG,EAAC,GAAEP,GAAE,YAAUA,GAAE,SAAS,OAAM,MAAM,4CAA4C;AAAE,QAAGA,GAAE,UAAS;AAAC,UAAIQ,KAAE,IAAI;AAAG,SAAGA,IAAE,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAER,GAAE,SAAS,CAAC,GAAE,GAAGQ,IAAE,GAAER,GAAE,SAAS,CAAC,GAAE,GAAGI,IAAE,GAAE,IAAGI,EAAC;AAAA,IAAC,OAAK;AAAC,UAAG,CAACR,GAAE,SAAS,OAAM,MAAM,+CAA+C;AAAE,WAAIQ,OAAKD,KAAE,IAAI,MAAGP,GAAE,UAAU,IAAGA,KAAE,IAAI,MAAG,GAAE,IAAE,GAAE,GAAGA,IAAE,GAAEQ,GAAE,CAAC,GAAE,GAAGR,IAAE,GAAEQ,GAAE,CAAC,GAAE,GAAGD,IAAE,GAAE,IAAGP,EAAC;AAAE,SAAGI,IAAE,IAAG,IAAGG,EAAC;AAAA,IAAC;AAAC,OAAGL,IAAE,GAAE,IAAGE,EAAC,GAAE,KAAK,EAAE,iBAAiBF,GAAE,EAAE,GAAE,sBAAqB,UAASD,EAAC,GAAE,GAAG,MAAKF,IAAEI,EAAC;AAAE,OAAE;AAAC,UAAG;AAAC,cAAMJ,KAAE,IAAI,GAAG,KAAK,iBAAgB,KAAK,cAAa,KAAK,aAAa;AAAE,YAAG,CAAC,KAAK,GAAE;AAAC,cAAIM,KAAEN;AAAE,gBAAM;AAAA,QAAC;AAAC,aAAK,EAAEA,EAAC;AAAA,MAAC,UAAC;AAAQ,WAAG,IAAI;AAAA,MAAC;AAAC,MAAAM,KAAE;AAAA,IAAM;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAIN,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,QAAQ,GAAE,GAAGA,IAAE,cAAc;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,wEAAwE,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,YAAY,GAAE,GAAGA,IAAE,wBAAwB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,0BAAwB,GAAGA,IAAE,kBAAkB,GAAE,GAAGE,IAAE,mCAAmC,GAAE,GAAG,MAAK,kBAAkB,GAAE,KAAK,EAAE,GAAG,qBAAoB,CAACF,IAAEC,OAAI;AAAC,WAAK,kBAAgBD,GAAE,KAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,EAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,qBAAoB,CAAAD,OAAG;AAAC,WAAK,kBAAgB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,KAAK,uBAAqB,GAAGA,IAAE,eAAe,GAAE,GAAGE,IAAE,6BAA6B,GAAE,GAAG,MAAK,eAAe,GAAE,KAAK,EAAE,EAAE,kBAAiB,CAACF,IAAEC,OAAI;AAAC,WAAK,eAAa,GAAG,MAAKD,IAAE,OAAG,CAAC,KAAK,CAAC,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,kBAAiB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAG,GAAGA,IAAE,gBAAgB,GAAE,GAAGE,IAAE,+BAA+B,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAACF,IAAEC,OAAI;AAAC,WAAK,gBAAcD,IAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,mBAAkB,CAAAD,OAAG;AAAC,WAAK,eAAa,QAAO,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,UAAQ,GAAG,UAAU,SAAQ,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,mBAAkB,aAAY,KAAE,GAAE,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,WAASA,GAAE,qBAAmB,GAAG,KAAK,GAAE,GAAE,GAAGA,GAAE,kBAAkB,CAAC,IAAE,wBAAuBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,aAAW,GAAG,KAAK,GAAE,GAAEA,GAAE,UAAU,IAAE,gBAAeA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,iBAAe,GAAG,KAAK,GAAE,GAAEA,GAAE,cAAc,IAAE,oBAAmBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,oBAAkB,GAAG,KAAK,GAAE,GAAEA,GAAE,iBAAiB,IAAE,uBAAsBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,WAASA,GAAE,mBAAiB,GAAG,KAAK,GAAE,GAAEA,GAAE,gBAAgB,IAAE,sBAAqBA,MAAG,GAAG,KAAK,GAAE,CAAC,GAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKD,IAAEC,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,EAAED,IAAEC,IAAEC,IAAE;AAAC,WAAO,KAAK,IAAE,EAAC,YAAW,CAAC,EAAC,GAAE,GAAG,MAAKF,IAAEE,IAAED,EAAC,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,YAAY;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4CAA4C,GAAE,GAAGA,IAAE,uBAAuB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,uBAAuB,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAACF,IAAEC,OAAI;AAAC,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,EAAE,WAAW,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,eAAc,CAAAD,OAAG;AAAC,SAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAEA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,eAAeA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC;AAAE,IAAI,KAAG,MAAK;AAAA,EAAC,YAAYD,IAAEC,IAAEC,IAAE;AAAC,SAAK,YAAUF,IAAE,KAAK,iBAAeC,IAAE,KAAK,oBAAkBC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,mBAAmB,SAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,MAAM;AAAA,IAAC,EAAE;AAAA,EAAC;AAAC;AAAE,SAAS,GAAGA,IAAE;AAAC,EAAAA,GAAE,YAAU,CAAC,GAAEA,GAAE,iBAAe,CAAC,GAAEA,GAAE,oBAAkB;AAAM;AAAC,SAAS,GAAGA,IAAE;AAAC,MAAG;AAAC,UAAMC,KAAE,IAAI,GAAGD,GAAE,WAAUA,GAAE,gBAAeA,GAAE,iBAAiB;AAAE,QAAG,CAACA,GAAE,EAAE,QAAOC;AAAE,IAAAD,GAAE,EAAEC,EAAC;AAAA,EAAC,UAAC;AAAQ,OAAGD,EAAC;AAAA,EAAC;AAAC;AAAC,GAAG,UAAU,QAAM,GAAG,UAAU;AAAM,IAAI,KAAG,cAAc,GAAE;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAM,IAAI,GAAGD,IAAEC,EAAC,GAAE,YAAW,aAAY,KAAE,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,CAAC,GAAE,KAAK,0BAAwB,OAAG,GAAGD,KAAE,KAAK,IAAE,IAAI,MAAG,GAAE,GAAEC,KAAE,IAAI,IAAE,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,KAAK,IAAE,IAAI,MAAG,GAAG,KAAK,GAAE,GAAE,GAAE,KAAK,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,CAAC,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,GAAE,GAAE,GAAE;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,GAAG,KAAK,GAAE,IAAG,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,YAAYD,IAAE;AAAC,OAAG,KAAK,GAAE,GAAE,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAM,cAAaA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,YAAU,CAAC,GAAE,gCAA+BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,8BAA4B,GAAE,GAAE,2BAA0BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,yBAAuB,GAAE,GAAE,+BAA8BA,MAAG,GAAG,KAAK,GAAE,GAAEA,GAAE,6BAA2B,GAAE,GAAE,6BAA4BA,OAAI,KAAK,0BAAwBA,GAAE,2BAAyB,QAAI,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKF,IAAEG,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,cAAY,OAAOF,KAAEA,KAAE,CAAC;AAAE,WAAO,KAAK,IAAE,cAAY,OAAOA,KAAEA,KAAEC,IAAE,GAAG,IAAI,GAAE,GAAG,MAAKH,IAAEI,IAAEH,EAAC,GAAE,GAAG,IAAI;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,QAAID,KAAE,IAAI;AAAG,OAAGA,IAAE,UAAU,GAAE,GAAGA,IAAE,WAAW,GAAE,GAAGA,IAAE,sBAAsB,GAAE,GAAGA,IAAE,iBAAiB,GAAE,GAAGA,IAAE,oBAAoB;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,IAAG,KAAK,CAAC;AAAE,UAAMC,KAAE,IAAI;AAAG,OAAGA,IAAE,4DAA4D,GAAE,GAAGA,IAAE,gBAAgB,GAAE,GAAGA,IAAE,qBAAqB,GAAE,GAAGA,IAAE,qCAAqC,GAAE,GAAGA,IAAE,iCAAiC,GAAEA,GAAE,EAAED,EAAC,GAAE,GAAGD,IAAEE,EAAC,GAAE,GAAG,MAAKF,EAAC,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAACA,IAAEC,OAAI;AAAC,WAAK,YAAU,CAAC;AAAE,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,UAAU,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,yBAAwB,CAAAD,OAAG;AAAC,WAAK,YAAU,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAmB,CAACA,IAAEC,OAAI;AAAC,WAAK,iBAAe,CAAC;AAAE,iBAAUA,MAAKD,GAAE,CAAAA,KAAE,GAAGC,EAAC,GAAE,KAAK,eAAe,KAAK,GAAGD,EAAC,CAAC;AAAE,SAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,oBAAmB,CAAAD,OAAG;AAAC,WAAK,iBAAe,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,4BAA0B,GAAGE,IAAE,sCAAsC,GAAE,GAAG,MAAK,oBAAoB,GAAE,KAAK,EAAE,GAAG,uBAAsB,CAACF,IAAEC,OAAI;AAAC,WAAK,oBAAkBD,GAAE,KAAK,CAAAA,OAAG,GAAG,MAAKA,IAAE,MAAG,CAAC,KAAK,CAAC,EAAE,GAAE,GAAG,MAAKC,EAAC;AAAA,IAAC,EAAE,GAAE,KAAK,EAAE,0BAA0B,uBAAsB,CAAAD,OAAG;AAAC,WAAK,oBAAkB,CAAC,GAAE,GAAG,MAAKA,EAAC;AAAA,IAAC,EAAE,IAAGA,KAAEA,GAAE,EAAE,GAAE,KAAK,SAAS,IAAI,WAAWA,EAAC,GAAE,IAAE;AAAA,EAAC;AAAC;AAAE,GAAG,UAAU,iBAAe,GAAG,UAAU,GAAE,GAAG,UAAU,SAAO,GAAG,UAAU,GAAE,GAAG,UAAU,aAAW,GAAG,UAAU,GAAE,GAAG,sBAAoB,SAASA,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,gBAAeC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,wBAAsB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAE,EAAC,aAAY,EAAC,kBAAiBC,GAAC,EAAC,CAAC;AAAC,GAAE,GAAG,oBAAkB,SAASD,IAAEC,IAAE;AAAC,SAAO,GAAG,IAAGD,IAAEC,EAAC;AAAC,GAAE,GAAG,mBAAiB;", "names": ["t", "e", "n", "r", "i", "s", "c", "h", "o", "a", "u"]}