import { getPaletteNames } from './palettes';

export interface UIElements {
  amplitudeValue: HTMLElement;
  frequencyValue: HTMLElement;
  paletteValue: HTMLElement;
  surfaceValue: HTMLElement;
  fpsValue: HTMLElement;
  loading: HTMLElement;
  hud: HTMLElement;
  cameraModal: HTMLElement;
  allowCameraBtn: HTMLElement;
  noCameraBtn: HTMLElement;
}

export class UIManager {
  private elements: UIElements;
  private paletteNames: string[];
  private surfaceNames = ['Plane', 'Cylindrical', 'Twisted'];
  
  // Sparkline data for parameter history
  private amplitudeHistory: number[] = [];
  private frequencyHistory: number[] = [];
  private maxHistoryLength = 60; // 1 second at 60fps
  
  constructor() {
    this.elements = this.getUIElements();
    this.paletteNames = getPaletteNames();
    this.setupEventListeners();
  }
  
  private getUIElements(): UIElements {
    const getElementById = (id: string): HTMLElement => {
      const element = document.getElementById(id);
      if (!element) {
        throw new Error(`Element with id '${id}' not found`);
      }
      return element;
    };
    
    return {
      amplitudeValue: getElementById('amplitude-value'),
      frequencyValue: getElementById('frequency-value'),
      paletteValue: getElementById('palette-value'),
      surfaceValue: getElementById('surface-value'),
      fpsValue: getElementById('fps-value'),
      loading: getElementById('loading'),
      hud: getElementById('hud'),
      cameraModal: getElementById('camera-modal'),
      allowCameraBtn: getElementById('allow-camera'),
      noCameraBtn: getElementById('no-camera')
    };
  }
  
  private setupEventListeners(): void {
    // Keyboard controls
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    
    // Prevent context menu on canvas
    const canvas = document.getElementById('canvas');
    if (canvas) {
      canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }
  }
  
  private handleKeyDown(event: KeyboardEvent): void {
    switch (event.code) {
      case 'Digit1':
        this.onSurfaceChange?.(0);
        break;
      case 'Digit2':
        this.onSurfaceChange?.(1);
        break;
      case 'Digit3':
        this.onSurfaceChange?.(2);
        break;
      case 'KeyP':
        this.onPaletteCycle?.();
        break;
      case 'Space':
        event.preventDefault();
        this.onPauseToggle?.();
        break;
      case 'KeyH':
        this.onStatsToggle?.();
        break;
    }
  }
  
  // Event callbacks (to be set by main application)
  onSurfaceChange?: (surfaceIndex: number) => void;
  onPaletteCycle?: () => void;
  onPauseToggle?: () => void;
  onStatsToggle?: () => void;
  onCameraAllow?: () => void;
  onCameraDecline?: () => void;
  
  // Update parameter displays
  updateAmplitude(value: number): void {
    this.elements.amplitudeValue.textContent = value.toFixed(2);
    this.addToHistory(this.amplitudeHistory, value);
    this.updateSparkline('amplitude', this.amplitudeHistory);
  }
  
  updateFrequency(value: number): void {
    this.elements.frequencyValue.textContent = value.toFixed(2);
    this.addToHistory(this.frequencyHistory, value);
    this.updateSparkline('frequency', this.frequencyHistory);
  }
  
  updatePalette(index: number): void {
    const paletteName = this.paletteNames[index % this.paletteNames.length];
    this.elements.paletteValue.textContent = paletteName;
  }
  
  updateSurface(index: number): void {
    const surfaceName = this.surfaceNames[index % this.surfaceNames.length];
    this.elements.surfaceValue.textContent = surfaceName;
  }
  
  updateFPS(fps: number): void {
    this.elements.fpsValue.textContent = fps.toString();
    
    // Color code FPS
    if (fps >= 50) {
      this.elements.fpsValue.style.color = '#00ff88';
    } else if (fps >= 30) {
      this.elements.fpsValue.style.color = '#ffaa00';
    } else {
      this.elements.fpsValue.style.color = '#ff6666';
    }
  }
  
  private addToHistory(history: number[], value: number): void {
    history.push(value);
    if (history.length > this.maxHistoryLength) {
      history.shift();
    }
  }
  
  private updateSparkline(paramName: string, history: number[]): void {
    if (history.length < 2) return;
    
    // Find or create sparkline container
    let sparklineContainer = document.querySelector(`.sparkline-${paramName}`) as HTMLElement;
    if (!sparklineContainer) {
      sparklineContainer = document.createElement('div');
      sparklineContainer.className = `sparkline sparkline-${paramName}`;
      
      // Add to the appropriate parameter row
      const paramRow = paramName === 'amplitude' 
        ? this.elements.amplitudeValue.parentElement
        : this.elements.frequencyValue.parentElement;
      
      if (paramRow) {
        paramRow.appendChild(sparklineContainer);
      }
    }
    
    // Create SVG path
    const width = 60;
    const height = 20;
    const min = Math.min(...history);
    const max = Math.max(...history);
    const range = max - min || 1;
    
    let pathData = '';
    history.forEach((value, index) => {
      const x = (index / (history.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      pathData += index === 0 ? `M${x},${y}` : ` L${x},${y}`;
    });
    
    sparklineContainer.innerHTML = `
      <svg width="${width}" height="${height}">
        <path d="${pathData}" stroke="#00ff88" stroke-width="1.5" fill="none" opacity="0.7"/>
      </svg>
    `;
  }
  
  // Show/hide loading
  showLoading(message?: string): void {
    this.elements.loading.style.display = 'block';
    if (message) {
      const loadingText = this.elements.loading.querySelector('div:last-child');
      if (loadingText) {
        loadingText.textContent = message;
      }
    }
  }
  
  hideLoading(): void {
    this.elements.loading.style.display = 'none';
  }
  
  // Camera modal
  showCameraModal(): void {
    this.elements.cameraModal.classList.remove('hidden');
    
    // Set up button handlers
    this.elements.allowCameraBtn.onclick = () => {
      this.hideCameraModal();
      this.onCameraAllow?.();
    };
    
    this.elements.noCameraBtn.onclick = () => {
      this.hideCameraModal();
      this.onCameraDecline?.();
    };
  }
  
  hideCameraModal(): void {
    this.elements.cameraModal.classList.add('hidden');
  }
  
  // Gesture status indicators
  updateGestureStatus(gestures: {
    pinch: boolean;
    wrist: boolean;
    flip: boolean;
    handDetected: boolean;
  }): void {
    // Find or create gesture status container
    let gestureContainer = document.querySelector('.gesture-status') as HTMLElement;
    if (!gestureContainer) {
      gestureContainer = document.createElement('div');
      gestureContainer.className = 'gesture-status';
      
      // Add indicators
      gestureContainer.innerHTML = `
        <div class="gesture-indicator pinch-indicator" title="Pinch gesture"></div>
        <div class="gesture-indicator wrist-indicator" title="Wrist height"></div>
        <div class="gesture-indicator flip-indicator" title="Palm flip"></div>
        <div class="gesture-indicator hand-indicator" title="Hand detected"></div>
      `;
      
      this.elements.hud.appendChild(gestureContainer);
    }
    
    // Update indicators
    const indicators = {
      pinch: gestureContainer.querySelector('.pinch-indicator') as HTMLElement,
      wrist: gestureContainer.querySelector('.wrist-indicator') as HTMLElement,
      flip: gestureContainer.querySelector('.flip-indicator') as HTMLElement,
      hand: gestureContainer.querySelector('.hand-indicator') as HTMLElement
    };
    
    indicators.pinch.classList.toggle('active', gestures.pinch);
    indicators.wrist.classList.toggle('active', gestures.wrist);
    indicators.flip.classList.toggle('active', gestures.flip);
    indicators.hand.classList.toggle('active', gestures.handDetected);
  }
  
  // Quality indicator
  updateQualityIndicator(quality: string, autoEnabled: boolean): void {
    let qualityIndicator = document.querySelector('.quality-indicator') as HTMLElement;
    if (!qualityIndicator) {
      qualityIndicator = document.createElement('div');
      qualityIndicator.className = 'quality-indicator';
      document.body.appendChild(qualityIndicator);
    }
    
    qualityIndicator.textContent = autoEnabled ? `Quality: ${quality} (Auto)` : `Quality: ${quality}`;
    qualityIndicator.className = `quality-indicator quality-${quality.toLowerCase()}`;
  }
  
  // Mouse fallback controls
  setupMouseFallback(): void {
    const canvas = document.getElementById('canvas') as HTMLCanvasElement;
    if (!canvas) return;
    
    let mouseAmplitude = 0.3;
    let mouseFrequency = 2.0;
    
    // Mouse wheel for amplitude
    canvas.addEventListener('wheel', (event) => {
      event.preventDefault();
      const delta = event.deltaY > 0 ? -0.05 : 0.05;
      mouseAmplitude = Math.max(0.05, Math.min(0.8, mouseAmplitude + delta));
      this.onMouseAmplitude?.(mouseAmplitude);
    });
    
    // Mouse Y position for frequency
    canvas.addEventListener('mousemove', (event) => {
      const rect = canvas.getBoundingClientRect();
      const y = (event.clientY - rect.top) / rect.height;
      mouseFrequency = 0.8 + (1.0 - y) * (6.0 - 0.8); // Inverted Y
      this.onMouseFrequency?.(mouseFrequency);
    });
    
    // Click for palette cycle
    canvas.addEventListener('click', () => {
      this.onPaletteCycle?.();
    });
    
    // Update HUD to show mouse controls
    const hints = this.elements.hud.querySelector('.hints');
    if (hints) {
      hints.innerHTML = `
        <div>• Mouse wheel → amplitude</div>
        <div>• Mouse Y → frequency</div>
        <div>• Click → change palette</div>
        <div>• Keys: 1-3 (surfaces), P (palette), Space (pause)</div>
      `;
    }
  }
  
  // Mouse control callbacks
  onMouseAmplitude?: (amplitude: number) => void;
  onMouseFrequency?: (frequency: number) => void;
  
  // Show error message
  showError(message: string): void {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(255, 0, 0, 0.9);
      color: white;
      padding: 20px;
      border-radius: 8px;
      z-index: 1000;
      max-width: 400px;
      text-align: center;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 5000);
  }
}
