/* Additional styles for Voxel Wave Sculptor */
/* Main styles are in index.html for better organization */

/* Sparkline for parameter history */
.sparkline {
  width: 60px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}

.sparkline svg {
  width: 100%;
  height: 100%;
}

.sparkline path {
  fill: none;
  stroke: #00ff88;
  stroke-width: 1.5;
  opacity: 0.7;
}

/* Gesture status indicators */
.gesture-status {
  display: flex;
  gap: 8px;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.gesture-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.gesture-indicator.active {
  background: #00ff88;
  box-shadow: 0 0 8px #00ff88;
}

/* Quality indicator */
.quality-indicator {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.quality-auto {
  color: #ffaa00;
}

.quality-high {
  color: #00ff88;
}

.quality-low {
  color: #ff6666;
}
