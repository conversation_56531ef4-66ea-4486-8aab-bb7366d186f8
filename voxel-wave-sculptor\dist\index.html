<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Voxel Wave Sculptor</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
        background: #000;
        color: #fff;
      }

      #app {
        position: relative;
        width: 100vw;
        height: 100vh;
      }

      /* Background video layer */
      #video-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        filter: blur(8px) brightness(0.3);
        z-index: 1;
      }

      /* WebGL canvas layer */
      #canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        cursor: crosshair;
      }

      /* HUD overlay */
      #hud {
        position: absolute;
        top: 20px;
        left: 20px;
        z-index: 3;
        background: rgba(0, 0, 0, 0.7);
        padding: 15px;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.4;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        min-width: 200px;
      }

      #hud .param {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }

      #hud .param-value {
        font-weight: bold;
        color: #00ff88;
      }

      #hud .hints {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        font-size: 12px;
        opacity: 0.8;
      }

      /* Camera permission modal */
      #camera-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        backdrop-filter: blur(10px);
      }

      #camera-modal.hidden {
        display: none;
      }

      .modal-content {
        background: rgba(20, 20, 20, 0.95);
        padding: 40px;
        border-radius: 12px;
        text-align: center;
        max-width: 500px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .modal-content h2 {
        margin-bottom: 20px;
        color: #00ff88;
      }

      .modal-content p {
        margin-bottom: 30px;
        line-height: 1.6;
        opacity: 0.9;
      }

      .modal-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
      }

      .btn-primary {
        background: #00ff88;
        color: #000;
      }

      .btn-primary:hover {
        background: #00cc6a;
        transform: translateY(-2px);
      }

      .btn-secondary {
        background: transparent;
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      /* Loading indicator */
      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 5;
        text-align: center;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid #00ff88;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Mobile responsiveness */
      @media (max-width: 768px) {
        #hud {
          top: 10px;
          left: 10px;
          right: 10px;
          font-size: 12px;
          padding: 10px;
        }

        .modal-content {
          margin: 20px;
          padding: 30px 20px;
        }

        .modal-buttons {
          flex-direction: column;
        }
      }
    </style>
    <script type="module" crossorigin src="/assets/index-D-U4N0OE.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CuExBOlt.css">
  </head>
  <body>
    <div id="app">
      <!-- Background video layer -->
      <video id="video-background" autoplay muted playsinline></video>

      <!-- WebGL canvas layer -->
      <canvas id="canvas"></canvas>

      <!-- Loading indicator -->
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <div>Initializing Voxel Wave Sculptor...</div>
      </div>

      <!-- HUD overlay -->
      <div id="hud">
        <div class="param">
          <span>Amplitude:</span>
          <span class="param-value" id="amplitude-value">0.00</span>
        </div>
        <div class="param">
          <span>Frequency:</span>
          <span class="param-value" id="frequency-value">0.00</span>
        </div>
        <div class="param">
          <span>Palette:</span>
          <span class="param-value" id="palette-value">8BIT</span>
        </div>
        <div class="param">
          <span>Surface:</span>
          <span class="param-value" id="surface-value">Plane</span>
        </div>
        <div class="param">
          <span>FPS:</span>
          <span class="param-value" id="fps-value">--</span>
        </div>

        <div class="hints">
          <div>• Pinch thumb+index → amplitude</div>
          <div>• Wrist height → frequency</div>
          <div>• Flip hand → change palette</div>
          <div>• Keys: 1-3 (surfaces), P (palette), Space (pause)</div>
        </div>
      </div>

      <!-- Camera permission modal -->
      <div id="camera-modal">
        <div class="modal-content">
          <h2>🎥 Camera Access</h2>
          <p>We use your camera locally for hand gesture control. Your video never leaves your device.</p>
          <div class="modal-buttons">
            <button id="allow-camera" class="btn btn-primary">Allow Camera</button>
            <button id="no-camera" class="btn btn-secondary">No Thanks</button>
          </div>
        </div>
      </div>
    </div>

  </body>
</html>
