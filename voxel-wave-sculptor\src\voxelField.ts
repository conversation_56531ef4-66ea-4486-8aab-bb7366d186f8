import * as THREE from 'three';
import { createShaderPaletteData } from './palettes';

// Surface types
export const SurfaceType = {
  PLANE: 0,
  CYLINDRICAL: 1,
  TWISTED: 2
} as const;

export type SurfaceType = typeof SurfaceType[keyof typeof SurfaceType];

// Quality settings
interface QualitySettings {
  gridCols: number;
  gridRows: number;
  voxelSize: number;
}

const QUALITY_LEVELS: { [key: string]: QualitySettings } = {
  HIGH: { gridCols: 120, gridRows: 60, voxelSize: 0.08 },
  MEDIUM: { gridCols: 80, gridRows: 40, voxelSize: 0.09 },
  LOW: { gridCols: 60, gridRows: 30, voxelSize: 0.1 }
};

export class VoxelField {
  private scene: THREE.Scene;
  private renderer: THREE.WebGLRenderer;
  private camera: THREE.PerspectiveCamera;
  
  // Voxel system
  private instancedMesh: THREE.InstancedMesh | null = null;
  private material: THREE.ShaderMaterial | null = null;
  private geometry: THREE.BoxGeometry;
  
  // Surface parameters
  private currentSurface: SurfaceType = SurfaceType.PLANE;
  private currentQuality: string = 'HIGH';
  
  // Animation parameters
  private animationParams = {
    time: 0,
    speed: 1.0,
    amplitude: 0.05,
    frequency: 2.0,
    complexity: 1.2,
    paletteIndex: 0
  };
  
  // Lighting
  private ambientLight: THREE.AmbientLight;
  private directionalLight: THREE.DirectionalLight;
  
  // Palette system
  private paletteTexture: THREE.DataTexture;
  private paletteData: Float32Array;
  private paletteSize: number;
  private numPalettes: number;
  
  // Performance monitoring
  private lastFPSCheck = 0;
  private frameCount = 0;
  private currentFPS = 60;
  private autoQualityEnabled = true;
  private lowFPSCounter = 0;
  
  constructor(canvas: HTMLCanvasElement) {
    // Initialize renderer
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: true,
      alpha: true,
      powerPreference: 'high-performance'
    });
    
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    
    // Initialize scene
    this.scene = new THREE.Scene();
    
    // Initialize camera
    this.camera = new THREE.PerspectiveCamera(
      70,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 2, 5);
    this.camera.lookAt(0, 0, 0);
    
    // Initialize lighting
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(this.ambientLight);
    
    this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    this.directionalLight.position.set(5, 5, 5);
    this.scene.add(this.directionalLight);
    
    // Initialize geometry
    this.geometry = new THREE.BoxGeometry(1, 1, 1);
    
    // Initialize palette system
    const paletteInfo = createShaderPaletteData();
    this.paletteData = paletteInfo.paletteData;
    this.paletteSize = paletteInfo.paletteSize;
    this.numPalettes = paletteInfo.numPalettes;
    
    this.paletteTexture = new THREE.DataTexture(
      this.paletteData,
      this.paletteSize,
      this.numPalettes,
      THREE.RGBFormat,
      THREE.FloatType
    );
    this.paletteTexture.needsUpdate = true;
    
    // Create initial voxel field
    this.createVoxelField();
    
    // Handle window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }
  
  private getShaders(): { vertex: string; fragment: string } {
    // Inline shaders for better compatibility
    const vertex = `
      // Vertex shader for voxel wave displacement
      attribute vec3 aBasePos;
      attribute vec3 aNormal;
      attribute float aPhaseJitter;

      uniform float time;
      uniform float speed;
      uniform float amplitude;
      uniform float frequency;
      uniform float complexity;
      uniform int uSurfaceId;

      varying vec3 vPosition;
      varying vec3 vNormal;
      varying float vWaveHeight;
      varying float vPhase;

      // Surface transformation functions
      vec3 applySurfaceTransform(vec3 basePos, int surfaceId) {
          vec3 worldPos = basePos;

          if (surfaceId == 1) {
              // Cylindrical strip (radius = 2.0)
              float radius = 2.0;
              float angle = basePos.x * 3.14159; // Map x to angle
              worldPos.x = radius * cos(angle);
              worldPos.z = radius * sin(angle);
              worldPos.y = basePos.y;
          }
          else if (surfaceId == 2) {
              // Twisted band (Mobius-like with 0.5 twist)
              float radius = 2.0;
              float angle = basePos.x * 3.14159;
              float twist = basePos.x * 0.5; // 0.5 twist

              worldPos.x = (radius + basePos.y * cos(twist)) * cos(angle);
              worldPos.z = (radius + basePos.y * cos(twist)) * sin(angle);
              worldPos.y = basePos.y * sin(twist);
          }
          // surfaceId == 0: Plane - no transformation needed

          return worldPos;
      }

      // Wave displacement calculation
      float calculateWaveDisplacement(vec2 flatPos, float time) {
          float x = flatPos.x;
          float y = flatPos.y;

          // Main wave equation as specified
          float wave1 = sin(frequency * sqrt(x*x + y*y) + time * speed);
          float wave2 = cos(complexity * x * y + time * 0.3);

          return amplitude * wave1 * wave2;
      }

      void main() {
          // Get the base position (flattened surface coordinates)
          vec3 basePos = aBasePos;
          vec2 flatPos = basePos.xy; // Use original x,y for wave calculation

          // Apply surface transformation to get world position
          vec3 worldPos = applySurfaceTransform(basePos, uSurfaceId);

          // Calculate wave displacement using original flat coordinates
          float waveHeight = calculateWaveDisplacement(flatPos, time + aPhaseJitter);

          // Apply displacement along surface normal
          vec3 displacedPos = worldPos + aNormal * waveHeight;

          // Calculate wave phase for scaling
          float phase = frequency * sqrt(flatPos.x*flatPos.x + flatPos.y*flatPos.y) + time * speed + aPhaseJitter;
          float scaleY = 1.0 + 0.25 * smoothstep(-1.0, 1.0, sin(phase));

          // Apply instance transformation
          vec4 instancePos = instanceMatrix * vec4(position, 1.0);

          // Scale the voxel
          instancePos.y *= scaleY;

          // Translate to displaced position
          instancePos.xyz += displacedPos;

          // Final transformation
          vec4 mvPosition = modelViewMatrix * instancePos;
          gl_Position = projectionMatrix * mvPosition;

          // Pass data to fragment shader
          vPosition = displacedPos;
          vNormal = normalize((modelMatrix * vec4(aNormal, 0.0)).xyz);
          vWaveHeight = waveHeight;
          vPhase = phase;
      }
    `;

    const fragment = `
      // Fragment shader for voxel coloring
      precision highp float;

      uniform float time;
      uniform int paletteIndex;
      uniform sampler2D uPaletteTexture;
      uniform float uPaletteSize;
      uniform float uNumPalettes;

      varying vec3 vPosition;
      varying vec3 vNormal;
      varying float vWaveHeight;
      varying float vPhase;

      // Lighting uniforms
      uniform vec3 uLightDirection;
      uniform vec3 uLightColor;
      uniform vec3 uAmbientColor;

      // Get color from palette texture
      vec3 getPaletteColor(int palette, float t) {
          // Clamp t to [0, 1]
          t = clamp(t, 0.0, 1.0);

          // Calculate texture coordinates
          float colorIndex = t * (uPaletteSize - 1.0);
          float u = (float(palette) + 0.5) / uNumPalettes;
          float v = (colorIndex + 0.5) / uPaletteSize;

          return texture2D(uPaletteTexture, vec2(v, u)).rgb;
      }

      // Quantize value for discrete color steps
      float quantize(float value, float steps) {
          return floor(value * steps) / steps;
      }

      void main() {
          // Normalize wave height to [0, 1] for color mapping
          float normalizedHeight = (vWaveHeight + 1.0) * 0.5;

          // Add temporal hue shift
          float timeShift = sin(time * 0.5) * 0.1;
          float colorT = clamp(normalizedHeight + timeShift, 0.0, 1.0);

          // Quantize for discrete color steps (8-16 levels)
          colorT = quantize(colorT, 12.0);

          // Get base color from palette
          vec3 baseColor = getPaletteColor(paletteIndex, colorT);

          // Calculate lighting
          vec3 normal = normalize(vNormal);
          vec3 lightDir = normalize(-uLightDirection);

          // Diffuse lighting
          float diffuse = max(dot(normal, lightDir), 0.0);

          // Add some rim lighting based on wave phase
          float rim = 1.0 - abs(dot(normal, vec3(0.0, 0.0, 1.0)));
          rim = pow(rim, 2.0) * 0.3;

          // Combine lighting
          vec3 ambient = uAmbientColor * baseColor;
          vec3 diffuseColor = uLightColor * baseColor * diffuse;
          vec3 rimColor = baseColor * rim;

          vec3 finalColor = ambient + diffuseColor + rimColor;

          // Add subtle wave-based brightness variation
          float waveBrightness = 1.0 + sin(vPhase) * 0.1;
          finalColor *= waveBrightness;

          // Tone mapping (simple Reinhard)
          finalColor = finalColor / (finalColor + vec3(1.0));

          // Gamma correction
          finalColor = pow(finalColor, vec3(1.0 / 2.2));

          gl_FragColor = vec4(finalColor, 1.0);
      }
    `;

    return { vertex, fragment };
  }
  
  private createVoxelField(): void {
    const quality = QUALITY_LEVELS[this.currentQuality];
    const { gridCols, gridRows, voxelSize } = quality;
    const totalInstances = gridCols * gridRows;

    // Get shaders
    const shaders = this.getShaders();
    
    // Create material
    this.material = new THREE.ShaderMaterial({
      vertexShader: shaders.vertex,
      fragmentShader: shaders.fragment,
      uniforms: {
        time: { value: 0 },
        speed: { value: this.animationParams.speed },
        amplitude: { value: this.animationParams.amplitude },
        frequency: { value: this.animationParams.frequency },
        complexity: { value: this.animationParams.complexity },
        paletteIndex: { value: this.animationParams.paletteIndex },
        uSurfaceId: { value: this.currentSurface },
        uPaletteTexture: { value: this.paletteTexture },
        uPaletteSize: { value: this.paletteSize },
        uNumPalettes: { value: this.numPalettes },
        uLightDirection: { value: this.directionalLight.position.clone().normalize() },
        uLightColor: { value: new THREE.Color(0xffffff) },
        uAmbientColor: { value: new THREE.Color(0x404040) }
      }
    });
    
    // Create instanced mesh
    this.instancedMesh = new THREE.InstancedMesh(
      this.geometry,
      this.material,
      totalInstances
    );
    
    // Generate surface data
    this.generateSurfaceData(gridCols, gridRows, voxelSize);
    
    // Add to scene
    this.scene.add(this.instancedMesh);
  }
  
  private generateSurfaceData(cols: number, rows: number, voxelSize: number): void {
    if (!this.instancedMesh) return;
    
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color();
    
    // Create instance attributes for base positions and normals
    const basePositions = new Float32Array(cols * rows * 3);
    const normals = new Float32Array(cols * rows * 3);
    const phaseJitter = new Float32Array(cols * rows);
    
    let instanceIndex = 0;
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        // Generate UV coordinates
        const u = (col / (cols - 1)) * 2 - 1; // [-1, 1]
        const v = (row / (rows - 1)) * 2 - 1; // [-1, 1]
        
        // Calculate base position and normal for current surface
        const { normal } = this.calculateSurfacePoint(u, v);
        
        // Store base position (flattened coordinates for wave calculation)
        const baseIndex = instanceIndex * 3;
        basePositions[baseIndex] = u;     // Keep original u for wave calc
        basePositions[baseIndex + 1] = v; // Keep original v for wave calc
        basePositions[baseIndex + 2] = 0; // Z is always 0 for base
        
        // Store surface normal
        normals[baseIndex] = normal.x;
        normals[baseIndex + 1] = normal.y;
        normals[baseIndex + 2] = normal.z;
        
        // Add phase jitter to prevent moiré patterns
        phaseJitter[instanceIndex] = (Math.random() - 0.5) * 0.6; // [-0.3, 0.3]
        
        // Set instance matrix (scale only, position handled in shader)
        matrix.makeScale(voxelSize, voxelSize, voxelSize);
        this.instancedMesh.setMatrixAt(instanceIndex, matrix);
        
        // Set initial color
        color.setHex(0x888888);
        this.instancedMesh.setColorAt(instanceIndex, color);
        
        instanceIndex++;
      }
    }
    
    // Add custom attributes to geometry
    this.geometry.setAttribute('aBasePos', new THREE.InstancedBufferAttribute(basePositions, 3));
    this.geometry.setAttribute('aNormal', new THREE.InstancedBufferAttribute(normals, 3));
    this.geometry.setAttribute('aPhaseJitter', new THREE.InstancedBufferAttribute(phaseJitter, 1));
    
    // Update instance matrices and colors
    this.instancedMesh.instanceMatrix.needsUpdate = true;
    if (this.instancedMesh.instanceColor) {
      this.instancedMesh.instanceColor.needsUpdate = true;
    }
  }
  
  private calculateSurfacePoint(u: number, v: number): { position: THREE.Vector3; normal: THREE.Vector3 } {
    const position = new THREE.Vector3();
    const normal = new THREE.Vector3();
    
    switch (this.currentSurface) {
      case SurfaceType.PLANE:
        position.set(u, v, 0);
        normal.set(0, 0, 1);
        break;
        
      case SurfaceType.CYLINDRICAL:
        {
          const radius = 2.0;
          const angle = u * Math.PI;
          position.set(
            radius * Math.cos(angle),
            v,
            radius * Math.sin(angle)
          );
          normal.set(Math.cos(angle), 0, Math.sin(angle));
        }
        break;
        
      case SurfaceType.TWISTED:
        {
          const radius = 2.0;
          const angle = u * Math.PI;
          const twist = u * 0.5;
          
          position.set(
            (radius + v * Math.cos(twist)) * Math.cos(angle),
            v * Math.sin(twist),
            (radius + v * Math.cos(twist)) * Math.sin(angle)
          );
          
          // Calculate normal for twisted surface (simplified)
          const tangentU = new THREE.Vector3(
            -(radius + v * Math.cos(twist)) * Math.sin(angle),
            0,
            (radius + v * Math.cos(twist)) * Math.cos(angle)
          );
          
          const tangentV = new THREE.Vector3(
            Math.cos(twist) * Math.cos(angle),
            Math.sin(twist),
            Math.cos(twist) * Math.sin(angle)
          );
          
          normal.crossVectors(tangentU, tangentV).normalize();
        }
        break;
    }
    
    return { position, normal };
  }
  
  // Public methods
  setSurface(surface: SurfaceType): void {
    if (this.currentSurface !== surface) {
      this.currentSurface = surface;
      if (this.material) {
        this.material.uniforms.uSurfaceId.value = surface;
      }
      // Regenerate surface data
      const quality = QUALITY_LEVELS[this.currentQuality];
      this.generateSurfaceData(quality.gridCols, quality.gridRows, quality.voxelSize);
    }
  }
  
  updateParams(params: {
    amplitude?: number;
    frequency?: number;
    paletteIndex?: number;
  }): void {
    if (!this.material) return;
    
    if (params.amplitude !== undefined) {
      this.animationParams.amplitude = params.amplitude;
      this.material.uniforms.amplitude.value = params.amplitude;
    }
    
    if (params.frequency !== undefined) {
      this.animationParams.frequency = params.frequency;
      this.material.uniforms.frequency.value = params.frequency;
    }
    
    if (params.paletteIndex !== undefined) {
      this.animationParams.paletteIndex = params.paletteIndex;
      this.material.uniforms.paletteIndex.value = params.paletteIndex;
    }
  }
  
  render(deltaTime: number): void {
    this.animationParams.time += deltaTime;
    
    if (this.material) {
      this.material.uniforms.time.value = this.animationParams.time;
    }
    
    // Performance monitoring
    this.frameCount++;
    const now = performance.now();
    if (now - this.lastFPSCheck > 1000) {
      this.currentFPS = this.frameCount;
      this.frameCount = 0;
      this.lastFPSCheck = now;
      
      // Auto quality adjustment
      if (this.autoQualityEnabled) {
        this.checkPerformance();
      }
    }
    
    this.renderer.render(this.scene, this.camera);
  }
  
  private checkPerformance(): void {
    if (this.currentFPS < 45) {
      this.lowFPSCounter++;
      if (this.lowFPSCounter >= 2 && this.currentQuality === 'HIGH') {
        this.setQuality('MEDIUM');
        console.log('Auto-reduced quality to MEDIUM');
      } else if (this.lowFPSCounter >= 4 && this.currentQuality === 'MEDIUM') {
        this.setQuality('LOW');
        console.log('Auto-reduced quality to LOW');
      }
    } else {
      this.lowFPSCounter = Math.max(0, this.lowFPSCounter - 1);
    }
  }
  
  setQuality(quality: string): void {
    if (this.currentQuality !== quality && QUALITY_LEVELS[quality]) {
      this.currentQuality = quality;
      this.recreateVoxelField();
    }
  }
  
  private recreateVoxelField(): void {
    // Remove old mesh
    if (this.instancedMesh) {
      this.scene.remove(this.instancedMesh);
      this.instancedMesh.dispose();
    }

    // Create new field
    this.createVoxelField();
  }
  
  getFPS(): number {
    return this.currentFPS;
  }
  
  getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }
  
  getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }
  
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }
  
  dispose(): void {
    window.removeEventListener('resize', this.onWindowResize.bind(this));
    
    if (this.instancedMesh) {
      this.scene.remove(this.instancedMesh);
      this.instancedMesh.dispose();
    }
    
    if (this.material) {
      this.material.dispose();
    }
    
    this.geometry.dispose();
    this.paletteTexture.dispose();
    this.renderer.dispose();
  }
}
